import { NextRequest, NextResponse } from 'next/server';
import { rateLimiters, getClientIdentifier } from '@/lib/security/utils';

export type RateLimitType = 'auth' | 'api_general' | 'api_strict' | 'admin_actions';

/**
 * Rate limiting middleware for API routes
 * This is production-ready and uses Redis for distributed rate limiting
 */
export async function withRateLimit(
  request: NextRequest,
  type: RateLimitType = 'api_general'
): Promise<NextResponse | null> {
  try {
    const identifier = getClientIdentifier(request);
    const rateLimiter = rateLimiters[type];
    
    if (!rateLimiter) {
      console.error(`Rate limiter not found for type: ${type}`);
      return null; // Allow request if rate limiter is not configured
    }

    const result = await rateLimiter.limit(identifier);
    
    if (!result.success) {
      // Rate limit exceeded
      const response = NextResponse.json(
        {
          error: 'Rate limit exceeded',
          message: 'Too many requests. Please try again later.',
          retryAfter: Math.round((result.reset - Date.now()) / 1000),
        },
        { status: 429 }
      );
      
      // Add rate limit headers
      response.headers.set('X-RateLimit-Limit', result.limit.toString());
      response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
      response.headers.set('X-RateLimit-Reset', result.reset.toString());
      response.headers.set('Retry-After', Math.round((result.reset - Date.now()) / 1000).toString());
      
      return response;
    }
    
    // Request is allowed, but we should add rate limit headers for transparency
    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit', result.limit.toString());
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
    response.headers.set('X-RateLimit-Reset', result.reset.toString());
    
    return null; // Allow request to continue
  } catch (error) {
    console.error('Rate limiting error:', error);
    // In case of error, allow the request to continue
    // This ensures the app doesn't break if Redis is temporarily unavailable
    return null;
  }
}

/**
 * Higher-order function to wrap API route handlers with rate limiting
 */
export function withRateLimitHandler(
  handler: (req: NextRequest) => Promise<NextResponse>,
  type: RateLimitType = 'api_general'
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    // Apply rate limiting
    const rateLimitResponse = await withRateLimit(req, type);
    if (rateLimitResponse) {
      return rateLimitResponse; // Rate limit exceeded
    }
    
    // Continue with the original handler
    return handler(req);
  };
}

/**
 * Utility function to check rate limit without consuming it
 * Useful for displaying rate limit info to users
 */
export async function checkRateLimit(
  request: NextRequest,
  type: RateLimitType = 'api_general'
): Promise<{
  allowed: boolean;
  limit: number;
  remaining: number;
  reset: number;
} | null> {
  try {
    const identifier = getClientIdentifier(request);
    const rateLimiter = rateLimiters[type];
    
    if (!rateLimiter) {
      return null;
    }

    // This is a dry run - it checks the limit without consuming it
    const result = await rateLimiter.limit(identifier);
    
    return {
      allowed: result.success,
      limit: result.limit,
      remaining: result.remaining,
      reset: result.reset,
    };
  } catch (error) {
    console.error('Rate limit check error:', error);
    return null;
  }
}
/**
 * Generates a rate limit key based on authenticated user ID
 * @param request The NextRequest object
 * @returns A user-specific rate limit key
 * @throws Error if no user ID is found in headers
 */
export function getUserRateLimitKey(request: NextRequest): string {
  const userId = request.headers.get('x-user-id');

  if (!userId) {
    throw new Error('User authentication required for rate limiting');
  }

  return `user:${userId}`;
}

/**
 * Admin-specific rate limiting with enhanced logging
 */

/**
 * Admin-specific rate limiting with enhanced logging
 */
export async function withAdminRateLimit(
  request: NextRequest,
  userId?: string
): Promise<NextResponse | null> {
  const rateLimitResponse = await withRateLimit(request, 'admin_actions');
  
  if (rateLimitResponse && userId) {
    // Log admin rate limit violations for security monitoring
    console.warn(`Admin rate limit exceeded for user ${userId}`, {
      ip: getClientIdentifier(request),
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString(),
    });
  }
  
  return rateLimitResponse;
}

/**
 * Authentication-specific rate limiting with enhanced security
 */
export async function withAuthRateLimit(
  request: NextRequest,
  action: 'login' | 'signup' | 'password_reset' = 'login'
): Promise<NextResponse | null> {
  const rateLimitResponse = await withRateLimit(request, 'auth');
  
  if (rateLimitResponse) {
    // Log authentication rate limit violations for security monitoring
    console.warn(`Authentication rate limit exceeded for ${action}`, {
      ip: getClientIdentifier(request),
      action,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString(),
    });
    
    // For auth endpoints, we might want to add additional security headers
    rateLimitResponse.headers.set('X-Content-Type-Options', 'nosniff');
    rateLimitResponse.headers.set('X-Frame-Options', 'DENY');
  }
  
  return rateLimitResponse;
}
