"use node";

/**
 * Google Vertex AI Image Embeddings Integration
 *
 * This module provides utilities for generating image embeddings using
 * Google's Vertex AI Multimodal Embeddings API.
 */

import { PredictionServiceClient } from "@google-cloud/aiplatform";

// Initialize the Google AI Platform client
let client: PredictionServiceClient | null = null;

function getClient(): PredictionServiceClient {
  if (!client) {
    client = new PredictionServiceClient({
      credentials: {
        client_email: process.env.GOOGLE_CLOUD_CLIENT_EMAIL,
        private_key: process.env.GOOGLE_CLOUD_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
  }
  return client;
}

/**
 * Configuration for the embedding service
 */
const EMBEDDING_CONFIG = {
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
  location: process.env.GOOGLE_CLOUD_LOCATION || "us-central1",
  model: "multimodalembedding@001",
};

/**
 * Generate embeddings for an image
 * 
 * @param imageUrl - URL of the image to process
 * @returns Promise<number[]> - Array of embedding values
 */
export async function generateImageEmbedding(imageUrl: string): Promise<number[]> {
  if (!EMBEDDING_CONFIG.projectId) {
    throw new Error("GCP_PROJECT_ID environment variable is required");
  }

  try {
    // Fetch the image data
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }

    const imageBuffer = await response.arrayBuffer();
    const encodedImage = Buffer.from(imageBuffer).toString("base64");

    // Prepare the request for Vertex AI
    const endpoint = `projects/${EMBEDDING_CONFIG.projectId}/locations/${EMBEDDING_CONFIG.location}/publishers/google/models/${EMBEDDING_CONFIG.model}`;

    const instance = {
      structValue: {
        fields: {
          image: {
            structValue: {
              fields: {
                bytesBase64Encoded: {
                  stringValue: encodedImage
                }
              }
            }
          }
        }
      }
    };

    const request = {
      endpoint,
      instances: [instance],
    };

    // Call the Vertex AI API
    const client = getClient();
    const [prediction] = await client.predict(request);

    if (!prediction.predictions || prediction.predictions.length === 0) {
      throw new Error("No predictions returned from Vertex AI");
    }

    // Extract the embedding from the response
    const predictionData = prediction.predictions[0];
    const embedding = predictionData?.structValue?.fields?.imageEmbedding?.listValue?.values?.map(
      (v) => v.numberValue || 0
    );

    if (!embedding || !Array.isArray(embedding)) {
      throw new Error("Invalid embedding format returned from Vertex AI");
    }

    return embedding;
  } catch (error) {
    console.error("Error generating image embedding:", error);
    throw new Error(`Failed to generate image embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Batch process multiple images for embedding generation
 * 
 * @param imageUrls - Array of image URLs to process
 * @param batchSize - Number of images to process concurrently
 * @returns Promise<Array<{ url: string; embedding: number[] | null; error?: string }>>
 */
export async function batchGenerateEmbeddings(
  imageUrls: string[],
  batchSize: number = 5
): Promise<Array<{ url: string; embedding: number[] | null; error?: string }>> {
  const results: Array<{ url: string; embedding: number[] | null; error?: string }> = [];

  // Process images in batches to avoid overwhelming the API
  for (let i = 0; i < imageUrls.length; i += batchSize) {
    const batch = imageUrls.slice(i, i + batchSize);
    
    const batchPromises = batch.map(async (url) => {
      try {
        const embedding = await generateImageEmbedding(url);
        return { url, embedding, error: undefined };
      } catch (error) {
        console.error(`Failed to process image ${url}:`, error);
        return { 
          url, 
          embedding: null, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Add a small delay between batches to be respectful to the API
    if (i + batchSize < imageUrls.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return results;
}