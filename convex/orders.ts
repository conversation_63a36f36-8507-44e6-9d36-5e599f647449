import { v } from "convex/values";
import { query, mutation, internalQuery, internalMutation } from "./_generated/server";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Doc } from "./_generated/dataModel";

// ============================================================================
// ORDER QUERIES
// ============================================================================

/**
 * Get orders with filtering, search, and pagination
 * PERFORMANCE: Uses proper indexes and pagination
 */
export const getOrders = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    filters: v.optional(v.object({
      status: v.optional(v.union(
        v.literal("new"),
        v.literal("sourcing"),
        v.literal("action_required"),
        v.literal("shipped"),
        v.literal("delivered"),
        v.literal("cancelled")
      )),
      userId: v.optional(v.id("users")),
      assignedTo: v.optional(v.id("users")),
      dateRange: v.optional(v.object({
        start: v.number(),
        end: v.number(),
      })),
    })),
    search: v.optional(v.string()),
    sortBy: v.optional(v.union(
      v.literal("_creationTime"),
      v.literal("totalAmount"),
      v.literal("status")
    )),
    sortOrder: v.optional(v.union(v.literal("asc"), v.literal("desc"))),
  },
  handler: async (ctx, { paginationOpts, filters, search, sortBy = "_creationTime", sortOrder = "desc" }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_VIEW);

    // Build query with proper index usage
    let query = ctx.db.query("orders");

    // Apply filters using indexes where possible
    if (filters?.status) {
      query = query.withIndex("by_status", q => q.eq("status", filters.status));
    } else if (filters?.userId) {
      query = query.withIndex("by_user", q => q.eq("userId", filters.userId));
    } else if (filters?.assignedTo) {
      query = query.withIndex("by_assigned_to", q => q.eq("assignedTo", filters.assignedTo));
    }

    // Apply sorting
    if (sortOrder === "desc") {
      query = query.order("desc");
    }

    // Get paginated results
    const paginationOptions = paginationOpts || { numItems: 20, cursor: null };
    const results = await query.paginate(paginationOptions);

    // Apply additional filters in code
    let filteredOrders = results.page;

    if (filters?.dateRange) {
      filteredOrders = filteredOrders.filter(order =>
        order._creationTime >= filters.dateRange!.start &&
        order._creationTime <= filters.dateRange!.end
      );
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredOrders = filteredOrders.filter(order =>
        order.trackingNumber?.toLowerCase().includes(searchLower) ||
        order.shippingAddress.name.toLowerCase().includes(searchLower) ||
        order.items.some(item => item.title.toLowerCase().includes(searchLower))
      );
    }

    // Enrich orders with user and admin info
    const enrichedOrders = await Promise.all(
      filteredOrders.map(async (order) => {
        const [user, assignedAdmin] = await Promise.all([
          ctx.db.get(order.userId),
          order.assignedTo ? ctx.db.get(order.assignedTo) : null,
        ]);

        return {
          ...order,
          user: user ? {
            id: user._id,
            name: user.name,
            email: user.email,
          } : null,
          assignedAdmin: assignedAdmin ? {
            id: assignedAdmin._id,
            name: assignedAdmin.name,
            email: assignedAdmin.email,
          } : null,
          itemsCount: order.items.length,
          lastCommunication: order.communicationHistory.length > 0 
            ? order.communicationHistory[order.communicationHistory.length - 1]
            : null,
        };
      })
    );

    return {
      ...results,
      page: enrichedOrders,
      totalFiltered: filteredOrders.length,
      hasFilters: !!(filters && Object.keys(filters).length > 0),
      hasSearch: !!search?.trim(),
    };
  },
});

/**
 * Get a single order with full details
 * PERFORMANCE: Loads all related data efficiently
 */
export const getOrder = query({
  args: { id: v.id("orders") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_VIEW);

    const order = await ctx.db.get(id);
    if (!order) {
      throw new Error("Order not found");
    }

    // Get related data in parallel
    const [user, assignedAdmin, products] = await Promise.all([
      ctx.db.get(order.userId),
      order.assignedTo ? ctx.db.get(order.assignedTo) : null,
      Promise.all(
        order.items.map(async (item) => {
          const product = await ctx.db.get(item.productId);
          return {
            ...item,
            product: product ? {
              id: product._id,
              title: product.title,
              images: product.images,
              status: product.status,
              currentPrice: product.finalPrice,
            } : null,
          };
        })
      ),
    ]);

    return {
      ...order,
      user: user ? {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
      } : null,
      assignedAdmin: assignedAdmin ? {
        id: assignedAdmin._id,
        name: assignedAdmin.name,
        email: assignedAdmin.email,
      } : null,
      items: products,
      timeline: [
        { event: "Order Created", timestamp: order._creationTime, status: "new" },
        ...order.communicationHistory.map(comm => ({
          event: comm.fromAdmin ? "Admin Message" : "Customer Message",
          timestamp: comm.timestamp,
          message: comm.message,
          adminUserId: comm.adminUserId,
        })),
      ].sort((a, b) => a.timestamp - b.timestamp),
    };
  },
});

/**
 * Get orders for the current user
 * PERFORMANCE: Uses user index for efficient filtering
 */
export const getMyOrders = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    status: v.optional(v.union(
      v.literal("new"),
      v.literal("sourcing"),
      v.literal("action_required"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled")
    )),
  },
  handler: async (ctx, { paginationOpts, status }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    let query = ctx.db
      .query("orders")
      .withIndex("by_user", q => q.eq("userId", userId))
      .order("desc");

    const paginationOptions = paginationOpts || { numItems: 10, cursor: null };
    const results = await query.paginate(paginationOptions);

    // Filter by status if provided
    let filteredOrders = results.page;
    if (status) {
      filteredOrders = filteredOrders.filter(order => order.status === status);
    }

    // Enrich with basic product info
    const enrichedOrders = await Promise.all(
      filteredOrders.map(async (order) => {
        const items = await Promise.all(
          order.items.map(async (item) => {
            const product = await ctx.db.get(item.productId);
            return {
              ...item,
              product: product ? {
                id: product._id,
                title: product.title,
                images: product.images.slice(0, 1), // Only first image for list view
                status: product.status,
              } : null,
            };
          })
        );

        return {
          ...order,
          items,
          itemsCount: order.items.length,
          hasUnreadMessages: order.communicationHistory.some(
            comm => comm.fromAdmin && comm.timestamp > (order._creationTime + 1000) // Simple unread logic
          ),
        };
      })
    );

    return {
      ...results,
      page: enrichedOrders,
      totalFiltered: filteredOrders.length,
    };
  },
});

/**
 * Get order statistics for dashboard
 * PERFORMANCE: Uses aggregation and efficient queries
 */
export const getOrderStats = query({
  args: {
    dateRange: v.optional(v.object({
      start: v.number(),
      end: v.number(),
    })),
  },
  handler: async (ctx, { dateRange }) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    // Get orders within date range or all orders
    const ordersQuery = ctx.db.query("orders");
    const orders = await ordersQuery.collect();

    let filteredOrders = orders;
    if (dateRange) {
      filteredOrders = orders.filter(order =>
        order._creationTime >= dateRange.start &&
        order._creationTime <= dateRange.end
      );
    }

    // Calculate statistics
    const stats = {
      total: filteredOrders.length,
      byStatus: {
        new: filteredOrders.filter(o => o.status === "new").length,
        sourcing: filteredOrders.filter(o => o.status === "sourcing").length,
        action_required: filteredOrders.filter(o => o.status === "action_required").length,
        shipped: filteredOrders.filter(o => o.status === "shipped").length,
        delivered: filteredOrders.filter(o => o.status === "delivered").length,
        cancelled: filteredOrders.filter(o => o.status === "cancelled").length,
      },
      totalRevenue: filteredOrders
        .filter(o => o.status === "delivered")
        .reduce((sum, order) => sum + order.totalAmount, 0),
      averageOrderValue: 0,
      pendingOrders: filteredOrders.filter(o => 
        ["new", "sourcing", "action_required", "shipped"].includes(o.status)
      ).length,
    };

    const deliveredOrders = filteredOrders.filter(o => o.status === "delivered");
    if (deliveredOrders.length > 0) {
      stats.averageOrderValue = stats.totalRevenue / deliveredOrders.length;
    }

    return stats;
  },
});

// ============================================================================
// ORDER MUTATIONS
// ============================================================================

/**
 * Create a new order
 * PERFORMANCE: Validates products and calculates pricing efficiently
 */
export const createOrder = mutation({
  args: {
    items: v.array(v.object({
      productId: v.id("products"),
      quantity: v.number(),
      selectedVariant: v.optional(v.object({
        type: v.string(),
        name: v.string(),
        value: v.string(),
        priceType: v.string(),
        finalPrice: v.number(),
      })),
      selectedQuantityTier: v.optional(v.object({
        minQuantity: v.number(),
        price: v.number(),
        currency: v.string(),
      })),
      selectedCustomServices: v.optional(v.array(v.object({
        name: v.string(),
        price: v.number(),
      }))),
    })),
    shippingAddress: v.object({
      name: v.string(),
      address: v.string(),
      city: v.string(),
      country: v.string(),
      postalCode: v.string(),
    }),
  },
  handler: async (ctx, { items, shippingAddress }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate items and calculate total
    let totalAmount = 0;
    const validatedItems = [];

    for (const item of items) {
      const product = await ctx.db.get(item.productId);
      if (!product) {
        throw new Error(`Product ${item.productId} not found`);
      }

      if (product.status !== "active") {
        throw new Error(`Product "${product.title}" is not available`);
      }

      if (item.quantity <= 0) {
        throw new Error("Quantity must be positive");
      }

      if (product.stockCount < item.quantity) {
        throw new Error(`Insufficient stock for "${product.title}"`);
      }

      // Calculate item price
      let itemPrice = product.finalPrice;
      let variantAdjustment = 0;
      let customServicesTotal = 0;

      // Apply variant pricing
      if (item.selectedVariant) {
        if (item.selectedVariant.priceType === "absolute") {
          itemPrice = item.selectedVariant.finalPrice;
        } else if (item.selectedVariant.priceType === "modifier") {
          variantAdjustment = item.selectedVariant.finalPrice;
          itemPrice += variantAdjustment;
        }
      }

      // Apply quantity tier pricing
      if (item.selectedQuantityTier) {
        itemPrice = item.selectedQuantityTier.price;
      }

      // Add custom services
      if (item.selectedCustomServices) {
        customServicesTotal = item.selectedCustomServices.reduce(
          (sum, service) => sum + service.price, 0
        );
      }

      const itemTotal = (itemPrice * item.quantity) + customServicesTotal;
      totalAmount += itemTotal;

      validatedItems.push({
        productId: item.productId,
        quantity: item.quantity,
        priceAtTime: itemPrice,
        title: product.title,
      });

      // Update stock count
      await ctx.db.patch(item.productId, {
        stockCount: product.stockCount - item.quantity,
      });
    }

    // Create the order
    const orderId = await ctx.db.insert("orders", {
      userId,
      items: validatedItems,
      status: "new",
      shippingAddress,
      communicationHistory: [],
      totalAmount,
      providerOrderData: {
        selectedVariant: items[0]?.selectedVariant || undefined,
        selectedQuantityTier: items[0]?.selectedQuantityTier || {
          minQuantity: 1,
          price: totalAmount,
          currency: "USD",
        },
        selectedCustomServices: items[0]?.selectedCustomServices || [],
        totalProviderCost: totalAmount * 0.7, // Assume 30% margin
        pricingBreakdown: {
          basePrice: totalAmount * 0.7,
          variantAdjustment: 0,
          quantityDiscount: 0,
          customServicesTotal: items[0]?.selectedCustomServices?.reduce((sum, s) => sum + s.price, 0) || 0,
          ourCommission: totalAmount * 0.3,
        },
      },
    });

    return { orderId, totalAmount, success: true };
  },
});

/**
 * Update order status
 * PERFORMANCE: Validates status transitions and maintains audit trail
 */
export const updateOrderStatus = mutation({
  args: {
    orderId: v.id("orders"),
    status: v.union(
      v.literal("new"),
      v.literal("sourcing"),
      v.literal("action_required"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled")
    ),
    trackingNumber: v.optional(v.string()),
    adminNote: v.optional(v.string()),
  },
  handler: async (ctx, { orderId, status, trackingNumber, adminNote }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_EDIT);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const order = await ctx.db.get(orderId);
    if (!order) {
      throw new Error("Order not found");
    }

    // Validate status transition
    const validTransitions: Record<string, string[]> = {
      new: ["sourcing", "cancelled"],
      sourcing: ["action_required", "shipped", "cancelled"],
      action_required: ["sourcing", "shipped", "cancelled"],
      shipped: ["delivered"],
      delivered: [], // Final state
      cancelled: [], // Final state
    };

    if (!validTransitions[order.status].includes(status)) {
      throw new Error(`Cannot transition from ${order.status} to ${status}`);
    }

    // Prepare update data
    const updateData: Partial<Doc<"orders">> = { status };
    if (trackingNumber) {
      updateData.trackingNumber = trackingNumber;
    }

    // Add communication history entry
    const communicationEntry = {
      message: adminNote || `Order status updated to ${status}`,
      fromAdmin: true,
      adminUserId: userId,
      timestamp: Date.now(),
    };

    updateData.communicationHistory = [
      ...order.communicationHistory,
      communicationEntry,
    ];

    await ctx.db.patch(orderId, updateData);

    return { success: true };
  },
});

/**
 * Add communication to an order
 * PERFORMANCE: Appends to existing communication history efficiently
 */
export const addOrderCommunication = mutation({
  args: {
    orderId: v.id("orders"),
    message: v.string(),
    fromAdmin: v.optional(v.boolean()),
  },
  handler: async (ctx, { orderId, message, fromAdmin = false }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const order = await ctx.db.get(orderId);
    if (!order) {
      throw new Error("Order not found");
    }

    // Check permissions
    if (fromAdmin) {
      await requirePermission(ctx, PERMISSIONS.ORDERS_COMMUNICATE);
    } else {
      // Verify user owns the order
      if (order.userId !== userId) {
        throw new Error("Not authorized to communicate on this order");
      }
    }

    if (!message.trim()) {
      throw new Error("Message cannot be empty");
    }

    const communicationEntry = {
      message: message.trim(),
      fromAdmin,
      adminUserId: fromAdmin ? userId : undefined,
      timestamp: Date.now(),
    };

    await ctx.db.patch(orderId, {
      communicationHistory: [
        ...order.communicationHistory,
        communicationEntry,
      ],
    });

    return { success: true };
  },
});

/**
 * Assign order to admin user
 * PERFORMANCE: Simple assignment with validation
 */
export const assignOrder = mutation({
  args: {
    orderId: v.id("orders"),
    adminUserId: v.id("users"),
  },
  handler: async (ctx, { orderId, adminUserId }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_ASSIGN);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const [order, adminUser] = await Promise.all([
      ctx.db.get(orderId),
      ctx.db.get(adminUserId),
    ]);

    if (!order) {
      throw new Error("Order not found");
    }

    if (!adminUser) {
      throw new Error("Admin user not found");
    }

    // Verify the user is actually an admin
    const adminRecord = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", q => q.eq("userId", adminUserId))
      .first();

    if (!adminRecord || !adminRecord.isActive) {
      throw new Error("User is not an active admin");
    }

    await ctx.db.patch(orderId, {
      assignedTo: adminUserId,
    });

    // Add communication entry
    const communicationEntry = {
      message: `Order assigned to ${adminUser.name}`,
      fromAdmin: true,
      adminUserId: userId,
      timestamp: Date.now(),
    };

    await ctx.db.patch(orderId, {
      communicationHistory: [
        ...order.communicationHistory,
        communicationEntry,
      ],
    });

    return { success: true };
  },
});

/**
 * Cancel an order
 * PERFORMANCE: Restores stock and maintains audit trail
 */
export const cancelOrder = mutation({
  args: {
    orderId: v.id("orders"),
    reason: v.string(),
    refundAmount: v.optional(v.number()),
  },
  handler: async (ctx, { orderId, reason, refundAmount }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const order = await ctx.db.get(orderId);
    if (!order) {
      throw new Error("Order not found");
    }

    // Check permissions - either admin or order owner
    const isAdmin = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", q => q.eq("userId", userId))
      .first()
      .then(admin => admin && admin.isActive);

    if (!isAdmin && order.userId !== userId) {
      throw new Error("Not authorized to cancel this order");
    }

    // Can only cancel certain statuses
    if (!["new", "sourcing", "action_required"].includes(order.status)) {
      throw new Error("Order cannot be cancelled at this stage");
    }

    // Restore stock for all items
    await Promise.all(
      order.items.map(async (item) => {
        const product = await ctx.db.get(item.productId);
        if (product) {
          await ctx.db.patch(item.productId, {
            stockCount: product.stockCount + item.quantity,
          });
        }
      })
    );

    // Update order status
    const communicationEntry = {
      message: `Order cancelled. Reason: ${reason}${refundAmount ? ` Refund amount: $${refundAmount}` : ''}`,
      fromAdmin: isAdmin || false,
      adminUserId: isAdmin ? userId : undefined,
      timestamp: Date.now(),
    };

    await ctx.db.patch(orderId, {
      status: "cancelled",
      communicationHistory: [
        ...order.communicationHistory,
        communicationEntry,
      ],
    });

    return { success: true };
  },
});

/**
 * Add issue resolution to an order
 * PERFORMANCE: Updates order with resolution data efficiently
 */
export const addOrderIssueResolution = mutation({
  args: {
    orderId: v.id("orders"),
    issue: v.string(),
    suggestedAlternatives: v.array(v.id("products")),
    resolution: v.optional(v.string()),
  },
  handler: async (ctx, { orderId, issue, suggestedAlternatives, resolution }) => {
    await requirePermission(ctx, PERMISSIONS.ORDERS_EDIT);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const order = await ctx.db.get(orderId);
    if (!order) {
      throw new Error("Order not found");
    }

    // Validate suggested alternatives exist
    const alternatives = await Promise.all(
      suggestedAlternatives.map(id => ctx.db.get(id))
    );

    if (alternatives.some(alt => !alt)) {
      throw new Error("One or more suggested alternatives not found");
    }

    await ctx.db.patch(orderId, {
      status: "action_required",
      issueResolution: {
        issue: issue.trim(),
        suggestedAlternatives,
        resolution: resolution?.trim(),
      },
    });

    // Add communication entry
    const communicationEntry = {
      message: `Issue identified: ${issue}. ${suggestedAlternatives.length} alternative(s) suggested.`,
      fromAdmin: true,
      adminUserId: userId,
      timestamp: Date.now(),
    };

    await ctx.db.patch(orderId, {
      communicationHistory: [
        ...order.communicationHistory,
        communicationEntry,
      ],
    });

    return { success: true };
  },
});
