"use node";

/**
 * Convex actions for handling image embeddings with Google Vertex AI
 */

import { action } from "./_generated/server";
import { v } from "convex/values";

type ConfigValidationResult = {
  isValid: boolean;
  projectId: string | null;
  location: string | null;
  hasCredentials: boolean;
  error?: string;
};

type EmbeddingStatsResult = {
  count: number;
  dimensions: number;
  avgMagnitude: number;
  minValue: number;
  maxValue: number;
  totalProducts: number;
  productsWithEmbeddings: number;
  coveragePercentage: number;
};

/**
 * Generate image embedding using Google Vertex AI
 * This action is called when a product image is uploaded
 */
export const generateImageEmbedding = action({
  args: {
    imageUrl: v.string(),
    productId: v.optional(v.id("products")),
  },
  handler: async (ctx, { imageUrl, productId }) => {
    // Note: ctx and productId parameters are for future use
    console.log("Generating embedding for:", imageUrl, "productId:", productId);
    try {
      // Import the embedding utility (dynamic import for server-side code)
      const { generateImageEmbedding } = await import(
        "../lib/ml/googleImageEmbeddings"
      );

      // Generate the embedding
      const embedding = await generateImageEmbedding(imageUrl);

      // Note: In a full implementation, this would update the product with the embedding
      // For now, we'll just return the embedding

      return embedding;
    } catch (error) {
      console.error("Error generating image embedding:", error);
      throw new Error(
        `Failed to generate image embedding: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  },
});

/**
 * Test the embedding system with sample text or image
 */
export const testEmbeddingSystem = action({
  args: {
    input: v.string(),
    type: v.union(v.literal("text"), v.literal("image")),
  },
  handler: async (
    ctx,
    { input, type },
  ): Promise<{
    success: boolean;
    embedding?: {
      dimensions: number;
      sampleValues: number[];
      inputType: string;
      inputValue: string;
    };
    stats?: EmbeddingStatsResult;
    config?: ConfigValidationResult;
    error?: string;
  }> => {
    console.log(
      "Testing with:",
      input,
      "Type:",
      type,
      "Context:",
      ctx ? "available" : "unavailable",
    );

    try {
      // Simple configuration check
      const projectId = process.env.GOOGLE_CLOUD_PROJECT_ID;

      if (!projectId) {
        return {
          success: false,
          error: "GOOGLE_CLOUD_PROJECT_ID environment variable is required",
        };
      }

      let embedding: number[];

      if (type === "image") {
        // Test image embedding
        const { generateImageEmbedding } = await import(
          "../lib/ml/googleImageEmbeddings"
        );
        embedding = await generateImageEmbedding(input);
      } else {
        // Test text embedding
        const { generateTextEmbedding } = await import(
          "../lib/ml/googleTextEmbeddings"
        );
        embedding = await generateTextEmbedding(input);
      }

      return {
        success: true,
        embedding: {
          dimensions: embedding.length,
          sampleValues: embedding.slice(0, 8), // First 8 values for inspection
          inputType: type,
          inputValue:
            input.length > 50 ? input.substring(0, 50) + "..." : input,
        },
      };
    } catch (error) {
      console.error("Error testing embedding system:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Unknown error occurred during embedding generation",
      };
    }
  },
});

/**
 * Action to generate and store image embedding for a product
 * This can be called from the frontend to generate embeddings
 */
export const generateAndStoreImageEmbedding = action({
  args: {
    imageUrl: v.string(),
    productId: v.id("products"),
  },
  handler: async (ctx, { imageUrl, productId }) => {
    try {
      // Generate the embedding using the ML utility
      const { generateImageEmbedding } = await import(
        "../lib/ml/googleImageEmbeddings"
      );
      const embedding = await generateImageEmbedding(imageUrl);

      console.log("Successfully generated embedding for product:", productId);
      return {
        success: true,
        productId,
        embedding,
      };
    } catch (error) {
      console.error("Error generating embedding:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});
