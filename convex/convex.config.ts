import { defineApp } from "convex/server";
import aggregate from "@convex-dev/aggregate/convex.config";

const app = defineApp();
// Reduced aggregates to essential ones only to fix TypeScript compilation issues
app.use(aggregate, { name: "productStats" });
app.use(aggregate, { name: "orderStats" });
app.use(aggregate, { name: "supplierStats" });
// Removed: productStock, orderCount, orderMonthlySales, groupBuyStats, userCount
// These will be replaced with direct queries or simpler aggregates
export default app;
