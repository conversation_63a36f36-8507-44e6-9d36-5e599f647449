import { v } from "convex/values";
import { query } from "./_generated/server";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { Doc, Id } from "./_generated/dataModel";

// ============================================================================
// DASHBOARD ANALYTICS
// ============================================================================

/**
 * Get comprehensive dashboard statistics
 * PERFORMANCE: Optimized queries with proper aggregation
 */
export const getDashboardStats = query({
  args: {
    dateRange: v.optional(v.object({
      start: v.number(),
      end: v.number(),
    })),
  },
  handler: async (ctx, { dateRange }) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = now - (7 * 24 * 60 * 60 * 1000);

    // Get all data in parallel for performance
    const [
      allProducts,
      allOrders,
      allUsers,
      allSuppliers,
      allGroupBuys,
      recentProducts,
      recentOrders,
      recentUsers,
    ] = await Promise.all([
      ctx.db.query("products").collect(),
      ctx.db.query("orders").collect(),
      ctx.db.query("users").collect(),
      ctx.db.query("suppliers").collect(),
      ctx.db.query("groupBuys").collect(),
      ctx.db.query("products")
        .filter(q => q.gt(q.field("_creationTime"), thirtyDaysAgo))
        .collect(),
      ctx.db.query("orders")
        .filter(q => q.gt(q.field("_creationTime"), thirtyDaysAgo))
        .collect(),
      ctx.db.query("users")
        .filter(q => q.gt(q.field("_creationTime"), thirtyDaysAgo))
        .collect(),
    ]);

    // Filter by date range if provided
    let filteredOrders = allOrders;
    let filteredProducts = allProducts;
    let filteredUsers = allUsers;

    if (dateRange) {
      filteredOrders = allOrders.filter(order =>
        order._creationTime >= dateRange.start &&
        order._creationTime <= dateRange.end
      );
      filteredProducts = allProducts.filter(product =>
        product._creationTime >= dateRange.start &&
        product._creationTime <= dateRange.end
      );
      filteredUsers = allUsers.filter(user =>
        user._creationTime >= dateRange.start &&
        user._creationTime <= dateRange.end
      );
    }

    // Calculate order statistics
    const orderStats = {
      total: filteredOrders.length,
      byStatus: {
        new: filteredOrders.filter(o => o.status === "new").length,
        sourcing: filteredOrders.filter(o => o.status === "sourcing").length,
        action_required: filteredOrders.filter(o => o.status === "action_required").length,
        shipped: filteredOrders.filter(o => o.status === "shipped").length,
        delivered: filteredOrders.filter(o => o.status === "delivered").length,
        cancelled: filteredOrders.filter(o => o.status === "cancelled").length,
      },
      totalRevenue: filteredOrders
        .filter(o => o.status === "delivered")
        .reduce((sum, order) => sum + order.totalAmount, 0),
      averageOrderValue: 0,
      recentGrowth: {
        last7Days: allOrders.filter(o => o._creationTime > sevenDaysAgo).length,
        last30Days: recentOrders.length,
      },
    };

    const deliveredOrders = filteredOrders.filter(o => o.status === "delivered");
    if (deliveredOrders.length > 0) {
      orderStats.averageOrderValue = orderStats.totalRevenue / deliveredOrders.length;
    }

    // Calculate product statistics
    const productStats = {
      total: filteredProducts.length,
      byStatus: {
        active: filteredProducts.filter(p => p.status === "active").length,
        inactive: filteredProducts.filter(p => p.status === "inactive").length,
        archived: filteredProducts.filter(p => p.status === "archived").length,
      },
      totalValue: filteredProducts
        .filter(p => p.status === "active")
        .reduce((sum, p) => sum + (p.finalPrice * p.stockCount), 0),
      averagePrice: filteredProducts.length > 0
        ? filteredProducts.reduce((sum, p) => sum + p.finalPrice, 0) / filteredProducts.length
        : 0,
      lowStock: filteredProducts.filter(p => p.status === "active" && p.stockCount < 10).length,
      recentGrowth: {
        last7Days: allProducts.filter(p => p._creationTime > sevenDaysAgo).length,
        last30Days: recentProducts.length,
      },
    };

    // Calculate user statistics
    const userStats = {
      total: filteredUsers.length,
      recentGrowth: {
        last7Days: allUsers.filter(u => u._creationTime > sevenDaysAgo).length,
        last30Days: recentUsers.length,
      },
      activeUsers: new Set(
        allOrders
          .filter(o => o._creationTime > thirtyDaysAgo)
          .map(o => o.userId)
      ).size,
    };

    // Calculate supplier statistics
    const supplierStats = {
      total: allSuppliers.length,
      active: allSuppliers.filter(s => s.isActive).length,
      averageRating: 0,
    };

    const ratedSuppliers = allSuppliers.filter(s => s.rating !== undefined);
    if (ratedSuppliers.length > 0) {
      supplierStats.averageRating = ratedSuppliers.reduce((sum, s) => sum + (s.rating || 0), 0) / ratedSuppliers.length;
    }

    // Calculate group buy statistics
    const groupBuyStats = {
      total: allGroupBuys.length,
      active: allGroupBuys.filter(gb => gb.status === "active").length,
      completed: allGroupBuys.filter(gb => gb.status === "completed").length,
      totalParticipants: allGroupBuys.reduce((sum, gb) => sum + gb.currentParticipants, 0),
    };

    // Calculate top performing products (by order frequency)
    const productOrderCounts = new Map<string, number>();
    allOrders.forEach(order => {
      order.items.forEach(item => {
        const count = productOrderCounts.get(item.productId) || 0;
        productOrderCounts.set(item.productId, count + item.quantity);
      });
    });

    const topProducts = Array.from(productOrderCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);

    const topProductsWithDetails = await Promise.all(
      topProducts.map(async ([productId, orderCount]) => {
        const product = await ctx.db.get(productId as Id<"products">);
        return {
          id: productId,
          title: product?.title || "Unknown Product",
          orderCount,
          revenue: allOrders
            .filter(o => o.status === "delivered")
            .reduce((sum, order) => {
              const item = order.items.find(i => i.productId === productId);
              return sum + (item ? item.priceAtTime * item.quantity : 0);
            }, 0),
        };
      })
    );

    return {
      overview: {
        totalRevenue: orderStats.totalRevenue,
        totalOrders: orderStats.total,
        totalProducts: productStats.total,
        totalUsers: userStats.total,
        averageOrderValue: orderStats.averageOrderValue,
      },
      orders: orderStats,
      products: productStats,
      users: userStats,
      suppliers: supplierStats,
      groupBuys: groupBuyStats,
      topProducts: topProductsWithDetails,
      trends: {
        ordersGrowth: {
          current: orderStats.recentGrowth.last30Days,
          previous: allOrders.filter(o => 
            o._creationTime > (thirtyDaysAgo * 2) && 
            o._creationTime <= thirtyDaysAgo
          ).length,
        },
        productsGrowth: {
          current: productStats.recentGrowth.last30Days,
          previous: allProducts.filter(p => 
            p._creationTime > (thirtyDaysAgo * 2) && 
            p._creationTime <= thirtyDaysAgo
          ).length,
        },
        usersGrowth: {
          current: userStats.recentGrowth.last30Days,
          previous: allUsers.filter(u => 
            u._creationTime > (thirtyDaysAgo * 2) && 
            u._creationTime <= thirtyDaysAgo
          ).length,
        },
      },
    };
  },
});

/**
 * Get revenue analytics over time
 * PERFORMANCE: Efficient time-based aggregation
 */
export const getRevenueAnalytics = query({
  args: {
    period: v.union(v.literal("7d"), v.literal("30d"), v.literal("90d"), v.literal("1y")),
    granularity: v.optional(v.union(v.literal("day"), v.literal("week"), v.literal("month"))),
  },
  handler: async (ctx, { period, granularity = "day" }) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    const now = Date.now();
    let startTime: number;
    let intervalMs: number;

    // Calculate time range and interval
    switch (period) {
      case "7d":
        startTime = now - (7 * 24 * 60 * 60 * 1000);
        intervalMs = 24 * 60 * 60 * 1000; // 1 day
        break;
      case "30d":
        startTime = now - (30 * 24 * 60 * 60 * 1000);
        intervalMs = granularity === "week" ? 7 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000;
        break;
      case "90d":
        startTime = now - (90 * 24 * 60 * 60 * 1000);
        intervalMs = granularity === "month" ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000;
        break;
      case "1y":
        startTime = now - (365 * 24 * 60 * 60 * 1000);
        intervalMs = 30 * 24 * 60 * 60 * 1000; // 1 month
        break;
    }

    // Get delivered orders in the time range
    const orders = await ctx.db
      .query("orders")
      .filter(q => 
        q.and(
          q.eq(q.field("status"), "delivered"),
          q.gt(q.field("_creationTime"), startTime)
        )
      )
      .collect();

    // Group orders by time intervals
    const intervals = new Map<number, { revenue: number; orderCount: number }>();
    
    // Initialize intervals
    for (let time = startTime; time <= now; time += intervalMs) {
      intervals.set(time, { revenue: 0, orderCount: 0 });
    }

    // Aggregate orders into intervals
    orders.forEach(order => {
      const intervalStart = Math.floor((order._creationTime - startTime) / intervalMs) * intervalMs + startTime;
      const existing = intervals.get(intervalStart) || { revenue: 0, orderCount: 0 };
      intervals.set(intervalStart, {
        revenue: existing.revenue + order.totalAmount,
        orderCount: existing.orderCount + 1,
      });
    });

    // Convert to array format for charts
    const data = Array.from(intervals.entries())
      .sort((a, b) => a[0] - b[0])
      .map(([timestamp, stats]) => ({
        timestamp,
        date: new Date(timestamp).toISOString().split('T')[0],
        revenue: stats.revenue,
        orderCount: stats.orderCount,
        averageOrderValue: stats.orderCount > 0 ? stats.revenue / stats.orderCount : 0,
      }));

    return {
      data,
      summary: {
        totalRevenue: orders.reduce((sum, order) => sum + order.totalAmount, 0),
        totalOrders: orders.length,
        averageOrderValue: orders.length > 0
          ? orders.reduce((sum, order) => sum + order.totalAmount, 0) / orders.length
          : 0,
        period,
        granularity,
      },
    };
  },
});

/**
 * Get product performance analytics
 * PERFORMANCE: Efficient product-based aggregation
 */
export const getProductAnalytics = query({
  args: {
    limit: v.optional(v.number()),
    sortBy: v.optional(v.union(
      v.literal("revenue"),
      v.literal("orders"),
      v.literal("quantity"),
      v.literal("rating")
    )),
  },
  handler: async (ctx, { limit = 20, sortBy = "revenue" }) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    const [allProducts, allOrders] = await Promise.all([
      ctx.db.query("products").collect(),
      ctx.db.query("orders").filter(q => q.eq(q.field("status"), "delivered")).collect(),
    ]);

    // Calculate metrics for each product
    const productMetrics = new Map<string, {
      product: Doc<"products">;
      revenue: number;
      orderCount: number;
      quantitySold: number;
      averageOrderValue: number;
    }>();

    // Initialize with all products
    allProducts.forEach(product => {
      productMetrics.set(product._id, {
        product,
        revenue: 0,
        orderCount: 0,
        quantitySold: 0,
        averageOrderValue: 0,
      });
    });

    // Aggregate order data
    allOrders.forEach(order => {
      order.items.forEach(item => {
        const metrics = productMetrics.get(item.productId);
        if (metrics) {
          const itemRevenue = item.priceAtTime * item.quantity;
          metrics.revenue += itemRevenue;
          metrics.orderCount += 1;
          metrics.quantitySold += item.quantity;
        }
      });
    });

    // Calculate average order values
    productMetrics.forEach(metrics => {
      if (metrics.orderCount > 0) {
        metrics.averageOrderValue = metrics.revenue / metrics.orderCount;
      }
    });

    // Sort and limit results
    const sortedProducts = Array.from(productMetrics.values())
      .sort((a, b) => {
        switch (sortBy) {
          case "revenue":
            return b.revenue - a.revenue;
          case "orders":
            return b.orderCount - a.orderCount;
          case "quantity":
            return b.quantitySold - a.quantitySold;
          default:
            return b.revenue - a.revenue;
        }
      })
      .slice(0, limit);

    return {
      products: sortedProducts.map(metrics => ({
        id: metrics.product._id,
        title: metrics.product.title,
        status: metrics.product.status,
        currentPrice: metrics.product.finalPrice,
        stockCount: metrics.product.stockCount,
        revenue: metrics.revenue,
        orderCount: metrics.orderCount,
        quantitySold: metrics.quantitySold,
        averageOrderValue: metrics.averageOrderValue,
        conversionRate: metrics.product.stockCount > 0
          ? (metrics.quantitySold / (metrics.quantitySold + metrics.product.stockCount)) * 100
          : 0,
      })),
      summary: {
        totalProducts: allProducts.length,
        productsWithSales: Array.from(productMetrics.values()).filter(m => m.revenue > 0).length,
        totalRevenue: Array.from(productMetrics.values()).reduce((sum, m) => sum + m.revenue, 0),
        averageRevenuePerProduct: 0,
      },
    };
  },
});

/**
 * Get customer analytics
 * PERFORMANCE: Efficient user-based aggregation
 */
export const getCustomerAnalytics = query({
  args: {
    limit: v.optional(v.number()),
    sortBy: v.optional(v.union(
      v.literal("totalSpent"),
      v.literal("orderCount"),
      v.literal("averageOrderValue"),
      v.literal("lastOrderDate")
    )),
  },
  handler: async (ctx, { limit = 20, sortBy = "totalSpent" }) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    const [allUsers, allOrders] = await Promise.all([
      ctx.db.query("users").collect(),
      ctx.db.query("orders").collect(),
    ]);

    // Calculate metrics for each customer
    const customerMetrics = new Map<string, {
      user: Doc<"users">;
      totalSpent: number;
      orderCount: number;
      averageOrderValue: number;
      lastOrderDate: number;
      firstOrderDate: number;
      deliveredOrders: number;
      cancelledOrders: number;
    }>();

    // Initialize with all users who have orders
    const usersWithOrders = new Set(allOrders.map(o => o.userId));
    allUsers.filter(user => usersWithOrders.has(user._id)).forEach(user => {
      customerMetrics.set(user._id, {
        user,
        totalSpent: 0,
        orderCount: 0,
        averageOrderValue: 0,
        lastOrderDate: 0,
        firstOrderDate: Date.now(),
        deliveredOrders: 0,
        cancelledOrders: 0,
      });
    });

    // Aggregate order data
    allOrders.forEach(order => {
      const metrics = customerMetrics.get(order.userId);
      if (metrics) {
        metrics.orderCount += 1;
        metrics.lastOrderDate = Math.max(metrics.lastOrderDate, order._creationTime);
        metrics.firstOrderDate = Math.min(metrics.firstOrderDate, order._creationTime);

        if (order.status === "delivered") {
          metrics.totalSpent += order.totalAmount;
          metrics.deliveredOrders += 1;
        } else if (order.status === "cancelled") {
          metrics.cancelledOrders += 1;
        }
      }
    });

    // Calculate average order values
    customerMetrics.forEach(metrics => {
      if (metrics.deliveredOrders > 0) {
        metrics.averageOrderValue = metrics.totalSpent / metrics.deliveredOrders;
      }
    });

    // Sort and limit results
    const sortedCustomers = Array.from(customerMetrics.values())
      .sort((a, b) => {
        switch (sortBy) {
          case "totalSpent":
            return b.totalSpent - a.totalSpent;
          case "orderCount":
            return b.orderCount - a.orderCount;
          case "averageOrderValue":
            return b.averageOrderValue - a.averageOrderValue;
          case "lastOrderDate":
            return b.lastOrderDate - a.lastOrderDate;
          default:
            return b.totalSpent - a.totalSpent;
        }
      })
      .slice(0, limit);

    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);

    return {
      customers: sortedCustomers.map(metrics => ({
        id: metrics.user._id,
        name: metrics.user.name,
        email: metrics.user.email,
        totalSpent: metrics.totalSpent,
        orderCount: metrics.orderCount,
        averageOrderValue: metrics.averageOrderValue,
        lastOrderDate: metrics.lastOrderDate,
        daysSinceLastOrder: Math.floor((now - metrics.lastOrderDate) / (24 * 60 * 60 * 1000)),
        isRecentCustomer: metrics.firstOrderDate > thirtyDaysAgo,
        deliveredOrders: metrics.deliveredOrders,
        cancelledOrders: metrics.cancelledOrders,
        successRate: metrics.orderCount > 0
          ? (metrics.deliveredOrders / metrics.orderCount) * 100
          : 0,
      })),
      summary: {
        totalCustomers: customerMetrics.size,
        totalRevenue: Array.from(customerMetrics.values()).reduce((sum, m) => sum + m.totalSpent, 0),
        averageCustomerValue: customerMetrics.size > 0
          ? Array.from(customerMetrics.values()).reduce((sum, m) => sum + m.totalSpent, 0) / customerMetrics.size
          : 0,
        repeatCustomers: Array.from(customerMetrics.values()).filter(m => m.orderCount > 1).length,
      },
    };
  },
});
