import { v } from "convex/values";
import { query, mutation, action, internalQuery, internalMutation } from "./_generated/server";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Doc, Id } from "./_generated/dataModel";
import { internal } from "./_generated/api";

// ============================================================================
// PRODUCT QUERIES
// ============================================================================

/**
 * Get products with advanced filtering, search, and pagination
 * PERFORMANCE: Uses proper indexes and pagination to avoid unbounded queries
 */
export const getProducts = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    filters: v.optional(v.object({
      status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
      supplierId: v.optional(v.id("suppliers")),
      minPrice: v.optional(v.number()),
      maxPrice: v.optional(v.number()),
      tags: v.optional(v.array(v.string())),
      hasStock: v.optional(v.boolean()),
    })),
    search: v.optional(v.string()),
    sortBy: v.optional(v.union(
      v.literal("title"),
      v.literal("finalPrice"),
      v.literal("stockCount"),
      v.literal("_creationTime")
    )),
    sortOrder: v.optional(v.union(v.literal("asc"), v.literal("desc"))),
  },
  handler: async (ctx, { paginationOpts, filters, search, sortBy = "_creationTime", sortOrder = "desc" }) => {
    // Check permissions for viewing products
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    // Build base query with proper index usage
    let query = ctx.db.query("products");

    // Apply filters using indexes where possible
    if (filters?.status) {
      query = query.withIndex("by_status", q => q.eq("status", filters.status));
    } else if (filters?.supplierId) {
      query = query.withIndex("by_supplier", q => q.eq("supplierId", filters.supplierId));
    } else {
      // Default to status index for active products
      query = query.withIndex("by_status", q => q.eq("status", "active"));
    }

    // Apply sorting
    if (sortOrder === "desc") {
      query = query.order("desc");
    }

    // Get paginated results
    const paginationOptions = paginationOpts || { numItems: 20, cursor: null };
    const results = await query.paginate(paginationOptions);

    // Apply additional filters in code (for complex conditions)
    let filteredProducts = results.page;

    if (filters?.minPrice || filters?.maxPrice) {
      filteredProducts = filteredProducts.filter(product => {
        if (filters.minPrice && product.finalPrice < filters.minPrice) return false;
        if (filters.maxPrice && product.finalPrice > filters.maxPrice) return false;
        return true;
      });
    }

    if (filters?.hasStock !== undefined) {
      filteredProducts = filteredProducts.filter(product => 
        filters.hasStock ? product.stockCount > 0 : product.stockCount === 0
      );
    }

    if (filters?.tags && filters.tags.length > 0) {
      filteredProducts = filteredProducts.filter(product =>
        filters.tags!.some(tag => product.tags.includes(tag))
      );
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.title.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Enrich products with related data
    const enrichedProducts = await Promise.all(
      filteredProducts.map(async (product) => {
        // Get supplier info (already denormalized in schema)
        const supplier = product.supplier || null;

        // Get pricing tiers count
        const pricingTiersCount = await ctx.db
          .query("productPricingTiers")
          .withIndex("by_product", q => q.eq("productId", product._id))
          .collect()
          .then(tiers => tiers.filter(tier => tier.isActive).length);

        // Get variants count
        const variantsCount = await ctx.db
          .query("productVariants")
          .withIndex("by_product", q => q.eq("productId", product._id))
          .collect()
          .then(variants => variants.filter(variant => variant.isActive).length);

        return {
          ...product,
          supplier,
          pricingTiersCount,
          variantsCount,
        };
      })
    );

    return {
      ...results,
      page: enrichedProducts,
      totalFiltered: filteredProducts.length,
      hasFilters: !!(filters && Object.keys(filters).length > 0),
      hasSearch: !!search?.trim(),
    };
  },
});

/**
 * Get a single product with all related data
 * PERFORMANCE: Uses proper indexes and batch loading
 */
export const getProduct = query({
  args: { id: v.id("products") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    // Get all related data in parallel for performance
    const [
      supplier,
      pricingTiers,
      variants,
      attributes,
      customServices,
      providerData,
      embedding
    ] = await Promise.all([
      // Get supplier details
      ctx.db.get(product.supplierId),
      
      // Get active pricing tiers
      ctx.db
        .query("productPricingTiers")
        .withIndex("by_product", q => q.eq("productId", id))
        .collect()
        .then(tiers => tiers.filter(tier => tier.isActive)),
      
      // Get active variants
      ctx.db
        .query("productVariants")
        .withIndex("by_product", q => q.eq("productId", id))
        .collect()
        .then(variants => variants.filter(variant => variant.isActive)),
      
      // Get attributes
      ctx.db
        .query("productAttributes")
        .withIndex("by_product", q => q.eq("productId", id))
        .collect(),
      
      // Get active custom services
      ctx.db
        .query("productCustomServices")
        .withIndex("by_product", q => q.eq("productId", id))
        .collect()
        .then(services => services.filter(service => service.isActive)),
      
      // Get provider data
      ctx.db
        .query("providerData")
        .withIndex("by_product", q => q.eq("productId", id))
        .collect()
        .then(data => data.filter(d => d.isActive)),
      
      // Get embedding data
      ctx.db
        .query("productEmbeddings")
        .withIndex("by_product", q => q.eq("productId", id))
        .first()
    ]);

    return {
      ...product,
      supplier,
      pricingTiers: pricingTiers.sort((a, b) => a.minQuantity - b.minQuantity),
      variants,
      attributes,
      customServices,
      providerData,
      hasEmbedding: !!embedding,
      embeddingModel: embedding?.model || null,
    };
  },
});

/**
 * Search products using full-text search
 * PERFORMANCE: Uses search index for efficient text search
 */
export const searchProducts = query({
  args: {
    searchTerm: v.string(),
    filters: v.optional(v.object({
      status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
      supplierId: v.optional(v.id("suppliers")),
    })),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { searchTerm, filters, limit = 20 }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    if (!searchTerm.trim()) {
      return [];
    }

    // Use search index for efficient text search
    let searchQuery = ctx.db
      .query("products")
      .withSearchIndex("search_products", q => q.search("title", searchTerm));

    // Apply filters
    if (filters?.status) {
      searchQuery = searchQuery.filter(q => q.eq(q.field("status"), filters.status));
    }
    if (filters?.supplierId) {
      searchQuery = searchQuery.filter(q => q.eq(q.field("supplierId"), filters.supplierId));
    }

    const results = await searchQuery.take(limit);

    // Enrich with basic supplier info
    return Promise.all(
      results.map(async (product) => ({
        ...product,
        supplier: product.supplier,
      }))
    );
  },
});

/**
 * Get unique tags for filtering
 * PERFORMANCE: Uses dedicated uniqueTags table for efficient tag queries
 */
export const getProductTags = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    const uniqueTags = await ctx.db
      .query("uniqueTags")
      .withIndex("by_tag")
      .collect();

    return uniqueTags.map(tag => tag.tag).sort();
  },
});

// ============================================================================
// PRODUCT MUTATIONS
// ============================================================================

/**
 * Create a new product
 * PERFORMANCE: Uses transactions and proper validation
 */
export const createProduct = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    curationNotes: v.string(),
    supplierId: v.id("suppliers"),
    basePrice: v.number(),
    currency: v.string(),
    finalPrice: v.number(),
    tags: v.array(v.string()),
    images: v.array(v.union(v.string(), v.id("_storage"))),
    stockCount: v.number(),
    providerId: v.string(),
    providerUrl: v.string(),
    pricingTiers: v.optional(v.array(v.object({
      minQuantity: v.number(),
      maxQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      discountPercentage: v.optional(v.number()),
    }))),
    variants: v.optional(v.array(v.object({
      type: v.string(),
      name: v.string(),
      value: v.string(),
      priceType: v.union(v.literal('modifier'), v.literal('absolute')),
      priceModifier: v.optional(v.number()),
      absolutePrice: v.optional(v.number()),
      currency: v.optional(v.string()),
      availableQuantity: v.optional(v.number()),
      images: v.optional(v.array(v.string())),
    }))),
    attributes: v.optional(v.array(v.object({
      name: v.string(),
      value: v.string(),
      type: v.union(v.literal("string"), v.literal("number"), v.literal("boolean")),
    }))),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_CREATE);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate supplier exists
    const supplier = await ctx.db.get(args.supplierId);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Validate required fields
    if (!args.title.trim()) {
      throw new Error("Product title is required");
    }
    if (!args.description.trim()) {
      throw new Error("Product description is required");
    }
    if (args.basePrice <= 0 || args.finalPrice <= 0) {
      throw new Error("Prices must be positive numbers");
    }
    if (args.stockCount < 0) {
      throw new Error("Stock count cannot be negative");
    }

    // Create the main product
    const productId = await ctx.db.insert("products", {
      title: args.title.trim(),
      description: args.description.trim(),
      curationNotes: args.curationNotes.trim(),
      supplierId: args.supplierId,
      basePrice: args.basePrice,
      currency: args.currency,
      finalPrice: args.finalPrice,
      tags: args.tags.map(tag => tag.trim()).filter(tag => tag.length > 0),
      images: args.images,
      stockCount: args.stockCount,
      status: "active",
      createdBy: userId,
      updatedBy: userId,
      providerId: args.providerId,
      providerUrl: args.providerUrl,
      lastScraped: Date.now(),
      supplier: {
        id: supplier._id,
        name: supplier.name,
      },
    });

    // Create pricing tiers if provided
    if (args.pricingTiers && args.pricingTiers.length > 0) {
      await Promise.all(
        args.pricingTiers.map(tier =>
          ctx.db.insert("productPricingTiers", {
            productId,
            minQuantity: tier.minQuantity,
            maxQuantity: tier.maxQuantity,
            price: tier.price,
            currency: tier.currency,
            discountPercentage: tier.discountPercentage,
            isActive: true,
          })
        )
      );
    }

    // Create variants if provided
    if (args.variants && args.variants.length > 0) {
      await Promise.all(
        args.variants.map(variant =>
          ctx.db.insert("productVariants", {
            productId,
            type: variant.type,
            name: variant.name,
            value: variant.value,
            priceType: variant.priceType,
            priceModifier: variant.priceModifier,
            absolutePrice: variant.absolutePrice,
            currency: variant.currency,
            availableQuantity: variant.availableQuantity,
            images: variant.images,
            isActive: true,
          })
        )
      );
    }

    // Create attributes if provided
    if (args.attributes && args.attributes.length > 0) {
      await Promise.all(
        args.attributes.map(attr =>
          ctx.db.insert("productAttributes", {
            productId,
            name: attr.name,
            value: attr.value,
            type: attr.type,
          })
        )
      );
    }

    // Update unique tags table
    const existingTags = await ctx.db
      .query("uniqueTags")
      .withIndex("by_tag")
      .collect();

    const existingTagSet = new Set(existingTags.map(t => t.tag));
    const newTags = args.tags.filter(tag => !existingTagSet.has(tag));

    await Promise.all(
      newTags.map(tag =>
        ctx.db.insert("uniqueTags", { tag })
      )
    );

    return { productId, success: true };
  },
});

/**
 * Update an existing product
 * PERFORMANCE: Only updates changed fields and maintains data consistency
 */
export const updateProduct = mutation({
  args: {
    id: v.id("products"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    curationNotes: v.optional(v.string()),
    basePrice: v.optional(v.number()),
    finalPrice: v.optional(v.number()),
    tags: v.optional(v.array(v.string())),
    images: v.optional(v.array(v.union(v.string(), v.id("_storage")))),
    stockCount: v.optional(v.number()),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
  },
  handler: async (ctx, { id, ...updates }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_EDIT);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    // Validate updates
    if (updates.title !== undefined && !updates.title.trim()) {
      throw new Error("Product title cannot be empty");
    }
    if (updates.description !== undefined && !updates.description.trim()) {
      throw new Error("Product description cannot be empty");
    }
    if (updates.basePrice !== undefined && updates.basePrice <= 0) {
      throw new Error("Base price must be positive");
    }
    if (updates.finalPrice !== undefined && updates.finalPrice <= 0) {
      throw new Error("Final price must be positive");
    }
    if (updates.stockCount !== undefined && updates.stockCount < 0) {
      throw new Error("Stock count cannot be negative");
    }

    // Prepare update object
    const updateData: Partial<Doc<"products">> & { updatedBy: Id<"users"> } = {
      updatedBy: userId,
    };

    if (updates.title !== undefined) updateData.title = updates.title.trim();
    if (updates.description !== undefined) updateData.description = updates.description.trim();
    if (updates.curationNotes !== undefined) updateData.curationNotes = updates.curationNotes.trim();
    if (updates.basePrice !== undefined) updateData.basePrice = updates.basePrice;
    if (updates.finalPrice !== undefined) updateData.finalPrice = updates.finalPrice;
    if (updates.stockCount !== undefined) updateData.stockCount = updates.stockCount;
    if (updates.status !== undefined) updateData.status = updates.status;
    if (updates.images !== undefined) updateData.images = updates.images;

    if (updates.tags !== undefined) {
      const cleanTags = updates.tags.map(tag => tag.trim()).filter(tag => tag.length > 0);
      updateData.tags = cleanTags;

      // Update unique tags table
      const existingTags = await ctx.db
        .query("uniqueTags")
        .withIndex("by_tag")
        .collect();

      const existingTagSet = new Set(existingTags.map(t => t.tag));
      const newTags = cleanTags.filter(tag => !existingTagSet.has(tag));

      await Promise.all(
        newTags.map(tag =>
          ctx.db.insert("uniqueTags", { tag })
        )
      );
    }

    await ctx.db.patch(id, updateData);

    return { success: true };
  },
});

/**
 * Delete a product (soft delete by setting status to archived)
 * PERFORMANCE: Maintains referential integrity
 */
export const deleteProduct = mutation({
  args: { id: v.id("products") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_DELETE);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    // Check if product is used in any active orders
    const activeOrders = await ctx.db
      .query("orders")
      .filter(q =>
        q.and(
          q.neq(q.field("status"), "cancelled"),
          q.neq(q.field("status"), "delivered")
        )
      )
      .collect();

    const hasActiveOrders = activeOrders.some(order =>
      order.items.some(item => item.productId === id)
    );

    if (hasActiveOrders) {
      throw new Error("Cannot delete product with active orders. Archive it instead.");
    }

    // Soft delete by archiving
    await ctx.db.patch(id, {
      status: "archived",
      updatedBy: userId,
    });

    // Deactivate related data
    const [pricingTiers, variants, customServices, providerData] = await Promise.all([
      ctx.db.query("productPricingTiers").withIndex("by_product", q => q.eq("productId", id)).collect(),
      ctx.db.query("productVariants").withIndex("by_product", q => q.eq("productId", id)).collect(),
      ctx.db.query("productCustomServices").withIndex("by_product", q => q.eq("productId", id)).collect(),
      ctx.db.query("providerData").withIndex("by_product", q => q.eq("productId", id)).collect(),
    ]);

    await Promise.all([
      ...pricingTiers.map(tier => ctx.db.patch(tier._id, { isActive: false })),
      ...variants.map(variant => ctx.db.patch(variant._id, { isActive: false })),
      ...customServices.map(service => ctx.db.patch(service._id, { isActive: false })),
      ...providerData.map(data => ctx.db.patch(data._id, { isActive: false })),
    ]);

    return { success: true };
  },
});

// ============================================================================
// VECTOR SEARCH ACTIONS
// ============================================================================

/**
 * Vector search for similar products
 * PERFORMANCE: Uses vector index for efficient similarity search
 * NOTE: Vector search is only available in actions per Convex limitations
 */
export const vectorSearchProducts = action({
  args: {
    query: v.string(),
    limit: v.optional(v.number()),
    filters: v.optional(v.object({
      status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
      supplierId: v.optional(v.id("suppliers")),
      minScore: v.optional(v.number()),
    })),
  },
  handler: async (ctx, { query, limit = 10, filters }) => {
    // Generate embedding for the query
    // NOTE: In a real implementation, you would call an external embedding service here
    // For now, we'll simulate this with a placeholder
    const embedding = await generateEmbedding(query);

    if (!embedding) {
      throw new Error("Failed to generate embedding for query");
    }

    // Perform vector search
    const searchResults = await ctx.vectorSearch("productEmbeddings", "by_embedding", {
      vector: embedding,
      limit: Math.min(limit, 50), // Cap at 50 for performance
    });

    // Filter by score if specified
    let filteredResults = searchResults;
    if (filters?.minScore) {
      filteredResults = searchResults.filter(result => result._score >= filters.minScore!);
    }

    // Get product IDs from embedding results
    const embeddingIds = filteredResults.map(result => result._id);

    // Load the actual products
    const products = await ctx.runQuery(internal.products.getProductsByEmbeddingIds, {
      embeddingIds,
      filters: {
        status: filters?.status,
        supplierId: filters?.supplierId,
      },
    });

    // Combine with scores and return
    return filteredResults.map(result => {
      const product = products.find(p => p.embeddingId === result._id);
      return {
        product,
        score: result._score,
        embeddingId: result._id,
      };
    }).filter(item => item.product !== undefined);
  },
});

/**
 * Internal query to get products by embedding IDs
 * PERFORMANCE: Uses proper indexes for efficient lookups
 */
export const getProductsByEmbeddingIds = internalQuery({
  args: {
    embeddingIds: v.array(v.id("productEmbeddings")),
    filters: v.optional(v.object({
      status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
      supplierId: v.optional(v.id("suppliers")),
    })),
  },
  handler: async (ctx, { embeddingIds, filters }) => {
    const products = [];

    for (const embeddingId of embeddingIds) {
      // Find product by embedding ID
      const embedding = await ctx.db.get(embeddingId);
      if (!embedding) continue;

      const product = await ctx.db.get(embedding.productId);
      if (!product) continue;

      // Apply filters
      if (filters?.status && product.status !== filters.status) continue;
      if (filters?.supplierId && product.supplierId !== filters.supplierId) continue;

      products.push({
        ...product,
        embeddingId,
      });
    }

    return products;
  },
});

/**
 * Generate and store embedding for a product
 * PERFORMANCE: Only generates embeddings when needed
 */
export const generateProductEmbedding = action({
  args: {
    productId: v.id("products"),
    forceRegenerate: v.optional(v.boolean()),
  },
  handler: async (ctx, { productId, forceRegenerate = false }) => {
    // Get the product
    const product = await ctx.runQuery(internal.products.getProductForEmbedding, { productId });
    if (!product) {
      throw new Error("Product not found");
    }

    // Check if embedding already exists
    const existingEmbedding = await ctx.runQuery(internal.products.getProductEmbedding, { productId });
    if (existingEmbedding && !forceRegenerate) {
      return { success: true, embeddingId: existingEmbedding._id, regenerated: false };
    }

    // Create text for embedding
    const embeddingText = `${product.title} ${product.description} ${product.tags.join(' ')}`;

    // Generate embedding
    const embedding = await generateEmbedding(embeddingText);
    if (!embedding) {
      throw new Error("Failed to generate embedding");
    }

    // Store or update embedding
    if (existingEmbedding) {
      await ctx.runMutation(internal.products.updateProductEmbedding, {
        embeddingId: existingEmbedding._id,
        embedding,
        model: "text-embedding-ada-002", // or your chosen model
      });
      return { success: true, embeddingId: existingEmbedding._id, regenerated: true };
    } else {
      const embeddingId = await ctx.runMutation(internal.products.createProductEmbedding, {
        productId,
        embedding,
        model: "text-embedding-ada-002", // or your chosen model
      });
      return { success: true, embeddingId, regenerated: false };
    }
  },
});

// ============================================================================
// INTERNAL HELPER FUNCTIONS
// ============================================================================

/**
 * Internal query to get product data for embedding generation
 */
export const getProductForEmbedding = internalQuery({
  args: { productId: v.id("products") },
  handler: async (ctx, { productId }) => {
    return await ctx.db.get(productId);
  },
});

/**
 * Internal query to get existing product embedding
 */
export const getProductEmbedding = internalQuery({
  args: { productId: v.id("products") },
  handler: async (ctx, { productId }) => {
    return await ctx.db
      .query("productEmbeddings")
      .withIndex("by_product", q => q.eq("productId", productId))
      .first();
  },
});

/**
 * Internal mutation to create product embedding
 */
export const createProductEmbedding = internalMutation({
  args: {
    productId: v.id("products"),
    embedding: v.array(v.float64()),
    model: v.string(),
  },
  handler: async (ctx, { productId, embedding, model }) => {
    return await ctx.db.insert("productEmbeddings", {
      productId,
      embedding,
      model,
      createdAt: Date.now(),
    });
  },
});

/**
 * Internal mutation to update product embedding
 */
export const updateProductEmbedding = internalMutation({
  args: {
    embeddingId: v.id("productEmbeddings"),
    embedding: v.array(v.float64()),
    model: v.string(),
  },
  handler: async (ctx, { embeddingId, embedding, model }) => {
    await ctx.db.patch(embeddingId, {
      embedding,
      model,
      createdAt: Date.now(),
    });
  },
});

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Generate embedding using external service
 * NOTE: This is a placeholder - implement with your chosen embedding service
 */
async function generateEmbedding(text: string): Promise<number[] | null> {
  try {
    // TODO: Implement actual embedding generation
    // Example with OpenAI:
    // const response = await fetch('https://api.openai.com/v1/embeddings', {
    //   method: 'POST',
    //   headers: {
    //     'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify({
    //     input: text,
    //     model: 'text-embedding-ada-002',
    //   }),
    // });
    // const data = await response.json();
    // return data.data[0].embedding;

    // For now, return null to indicate embedding generation is not implemented
    console.warn(`Embedding generation not implemented for text: ${text.substring(0, 50)}...`);
    return null;
  } catch (error) {
    console.error("Error generating embedding:", error);
    return null;
  }
}
