// convex/utils/queryUtils.ts
// Utility functions for query optimization and error handling

/**
 * Wraps a query with timeout and error handling
 * PERFORMANCE: Prevents long-running queries from consuming resources
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number = 30000, // 30 second default timeout
  operation: string = "Query"
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(new Error(`${operation} timed out after ${timeoutMs}ms`));
    }, timeoutMs);
  });

  try {
    return await Promise.race([promise, timeoutPromise]);
  } catch (error) {
    console.error(`${operation} failed:`, error);
    throw error;
  }
}

/**
 * Safely executes a batch operation with error handling
 * PERFORMANCE: Prevents one failed item from breaking entire batch
 */
export async function safeBatchOperation<T, R>(
  items: T[],
  operation: (item: T) => Promise<R>,
  batchSize: number = 10,
  continueOnError: boolean = true
): Promise<{ results: R[]; errors: Array<{ item: T; error: Error }> }> {
  const results: R[] = [];
  const errors: Array<{ item: T; error: Error }> = [];

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    
    const batchPromises = batch.map(async (item) => {
      try {
        const result = await operation(item);
        return { success: true, result, item };
      } catch (error) {
        return { 
          success: false, 
          error: error instanceof Error ? error : new Error(String(error)), 
          item 
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    
    for (const result of batchResults) {
      if (result.success) {
        results.push(result.result as R);
      } else {
        errors.push({ item: result.item, error: result.error as Error });
        
        if (!continueOnError) {
          throw result.error;
        }
      }
    }
  }

  return { results, errors };
}

/**
 * Validates pagination options to prevent abuse
 * SECURITY: Prevents excessive resource consumption
 */
export function validatePaginationOpts(paginationOpts?: { numItems: number; cursor?: string | null }) {
  if (!paginationOpts) return;
  
  const { numItems } = paginationOpts;
  
  if (numItems <= 0) {
    throw new Error("numItems must be positive");
  }
  
  if (numItems > 1000) {
    throw new Error("numItems cannot exceed 1000 for performance reasons");
  }
}

/**
 * Creates a safe query executor with built-in optimizations
 * PERFORMANCE: Combines timeout, validation, and error handling
 */
export function createSafeQueryExecutor(defaultTimeout: number = 30000) {
  return async function executeQuery<T>(
    queryFn: () => Promise<T>,
    options: {
      timeout?: number;
      operation?: string;
      retries?: number;
    } = {}
  ): Promise<T> {
    const { timeout = defaultTimeout, operation = "Query", retries = 0 } = options;
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await withTimeout(queryFn(), timeout, operation);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt < retries) {
          // Exponential backoff for retries
          const delay = Math.min(1000 * Math.pow(2, attempt), 5000);
          await new Promise(resolve => setTimeout(resolve, delay));
          console.warn(`${operation} attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error);
        }
      }
    }
    
    throw lastError;
  };
}

/**
 * Optimized batch fetcher for related entities
 * PERFORMANCE: Reduces N+1 queries by batching related entity fetches
 */
export async function batchFetchEntities<TId, TEntity>(
  ids: TId[],
  fetchFn: (id: TId) => Promise<TEntity | null>,
  batchSize: number = 50
): Promise<Map<TId, TEntity | null>> {
  const resultMap = new Map<TId, TEntity | null>();
  
  // Remove duplicates
  const uniqueIds = Array.from(new Set(ids));
  
  for (let i = 0; i < uniqueIds.length; i += batchSize) {
    const batch = uniqueIds.slice(i, i + batchSize);
    
    const batchResults = await Promise.all(
      batch.map(async (id) => {
        try {
          const entity = await fetchFn(id);
          return { id, entity };
        } catch (error) {
          console.warn(`Failed to fetch entity with id ${id}:`, error);
          return { id, entity: null };
        }
      })
    );
    
    batchResults.forEach(({ id, entity }) => {
      resultMap.set(id, entity);
    });
  }
  
  return resultMap;
}
