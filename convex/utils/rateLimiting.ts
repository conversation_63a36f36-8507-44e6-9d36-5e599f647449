// convex/utils/rateLimiting.ts
// Rate limiting utilities for expensive operations

/**
 * Simple in-memory rate limiter for Convex functions
 * COST OPTIMIZATION: Prevents abuse of expensive operations like vector search
 */
class RateLimiter {
  protected requests: Map<string, number[]> = new Map();

  constructor(
    protected maxRequests: number,
    protected windowMs: number
  ) {}
  
  /**
   * Check if a request is allowed for the given key
   */
  isAllowed(key: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    
    // Remove expired requests
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(key, validRequests);
    
    return true;
  }
  
  /**
   * Get remaining requests for a key
   */
  getRemainingRequests(key: string): number {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    return Math.max(0, this.maxRequests - validRequests.length);
  }
  
  /**
   * Get time until next request is allowed
   */
  getTimeUntilReset(key: string): number {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    
    if (requests.length === 0) return 0;
    
    const oldestRequest = Math.min(...requests);
    return Math.max(0, this.windowMs - (now - oldestRequest));
  }
  
  /**
   * Clear all rate limit data (for testing)
   */
  clear(): void {
    this.requests.clear();
  }
}

// Rate limiters for different operations
export const vectorSearchLimiter = new RateLimiter(10, 60000); // 10 requests per minute
export const embeddingGenerationLimiter = new RateLimiter(5, 60000); // 5 requests per minute
export const importLimiter = new RateLimiter(2, 300000); // 2 imports per 5 minutes
export const batchOperationLimiter = new RateLimiter(3, 60000); // 3 batch ops per minute

/**
 * Rate limiting decorator for Convex functions
 */
export function withRateLimit(
  limiter: RateLimiter,
  getKey: (args: Record<string, unknown>) => string = () => "global"
) {
  return function <T extends (...args: unknown[]) => Promise<unknown> | unknown>(
    target: T
  ): T {
    return (async (...args: unknown[]) => {
      const functionArgs = args[1] as Record<string, unknown>;
      const key = getKey(functionArgs); // args[1] is the function arguments in Convex

      if (!limiter.isAllowed(key)) {
        const timeUntilReset = limiter.getTimeUntilReset(key);
        throw new Error(
          `Rate limit exceeded. Try again in ${Math.ceil(timeUntilReset / 1000)} seconds.`
        );
      }

      const result = target(...args);
      return result instanceof Promise ? await result : result;
    }) as T;
  };
}

/**
 * Get rate limit key for user-based rate limiting
 * NOTE: In a real implementation, you'd want to use async auth or pass userId in function args
 */
export function getUserRateLimitKey(args: Record<string, unknown>): string {
  // Try to extract userId from function arguments
  const userId = args.userId as string | undefined;

  // Fallback to a generic key if no userId provided
  return userId || "anonymous";
}

/**
 * Rate limit check function for use in mutations/actions
 */
export function checkRateLimit(
  limiter: RateLimiter,
  key: string,
  operation: string
): void {
  if (!limiter.isAllowed(key)) {
    const timeUntilReset = limiter.getTimeUntilReset(key);
    const remaining = limiter.getRemainingRequests(key);
    
    throw new Error(
      `Rate limit exceeded for ${operation}. ` +
      `${remaining} requests remaining. ` +
      `Try again in ${Math.ceil(timeUntilReset / 1000)} seconds.`
    );
  }
}

/**
 * Adaptive rate limiting based on system load
 */
export class AdaptiveRateLimiter extends RateLimiter {
  private systemLoad: number = 0;
  
  constructor(
    baseMaxRequests: number,
    windowMs: number,
    private loadThreshold: number = 0.8
  ) {
    super(baseMaxRequests, windowMs);
  }
  
  /**
   * Update system load (0-1 scale)
   */
  updateSystemLoad(load: number): void {
    this.systemLoad = Math.max(0, Math.min(1, load));
  }
  
  /**
   * Get adjusted max requests based on system load
   */
  private getAdjustedMaxRequests(): number {
    if (this.systemLoad > this.loadThreshold) {
      const reduction = (this.systemLoad - this.loadThreshold) / (1 - this.loadThreshold);
      return Math.ceil(this.maxRequests * (1 - reduction * 0.5)); // Reduce by up to 50%
    }
    return this.maxRequests;
  }
  
  isAllowed(key: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    const validRequests = requests.filter(time => now - time < this.windowMs);
    const adjustedMaxRequests = this.getAdjustedMaxRequests();
    
    if (validRequests.length >= adjustedMaxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(key, validRequests);
    
    return true;
  }
}

// Adaptive rate limiters for high-load scenarios
export const adaptiveVectorSearchLimiter = new AdaptiveRateLimiter(10, 60000, 0.7);
export const adaptiveImportLimiter = new AdaptiveRateLimiter(2, 300000, 0.8);
