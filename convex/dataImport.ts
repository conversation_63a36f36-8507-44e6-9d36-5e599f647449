import { v } from "convex/values";
import { mutation, action, internalMutation, internalQuery } from "./_generated/server";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";
import { Doc, Id } from "./_generated/dataModel";

// ============================================================================
// ALIBABA DATA IMPORT FUNCTIONS
// ============================================================================

/**
 * Import scraped Alibaba product data
 * PERFORMANCE: Batch processing with proper validation and error handling
 * NOTE: Based on user memories about Alibaba data structure
 */
export const importAlibabaProducts = action({
  args: {
    products: v.array(v.object({
      // Core product data
      title: v.string(),
      description: v.string(),
      providerId: v.string(),
      providerUrl: v.string(),
      
      // Pricing data - derived from quantity-based offers
      basePrice: v.number(),
      currency: v.string(),
      quantityOffers: v.optional(v.array(v.object({
        minQuantity: v.number(),
        maxQuantity: v.optional(v.number()),
        price: v.number(),
        currency: v.string(),
      }))),
      
      // Image data - prioritize reliable sources
      productImagePreviews: v.optional(v.array(v.string())),
      productImageDescriptions: v.optional(v.array(v.string())),
      // Note: imageEmbedding is usually empty per user feedback
      
      // Supplier/vendor information
      supplierName: v.string(),
      supplierContact: v.optional(v.object({
        email: v.optional(v.string()),
        phone: v.optional(v.string()),
        address: v.optional(v.string()),
      })),
      supplierRating: v.optional(v.number()),
      
      // Product attributes and variants
      attributes: v.optional(v.array(v.object({
        name: v.string(),
        value: v.string(),
        type: v.optional(v.string()),
      }))),
      variants: v.optional(v.array(v.object({
        type: v.string(),
        name: v.string(),
        value: v.string(),
        priceModifier: v.optional(v.number()),
        images: v.optional(v.array(v.string())),
      }))),
      
      // Stock and availability
      stockCount: v.optional(v.number()),
      minOrderQuantity: v.optional(v.number()),
      
      // Categories and tags
      category: v.optional(v.string()),
      tags: v.optional(v.array(v.string())),
      
      // Raw scraped data for reference
      rawScrapedData: v.optional(v.any()),
    })),
    batchSize: v.optional(v.number()),
    skipExisting: v.optional(v.boolean()),
  },
  handler: async (ctx, { products, batchSize = 50, skipExisting = true }) => {
    // Process products in batches to avoid timeout
    const results = {
      total: products.length,
      processed: 0,
      created: 0,
      updated: 0,
      skipped: 0,
      errors: [] as Array<{ index: number; error: string; productId?: string }>,
    };

    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);
      
      try {
        const batchResult = await ctx.runMutation(internal.dataImport.processBatch, {
          products: batch,
          startIndex: i,
          skipExisting,
        });
        
        results.processed += batchResult.processed;
        results.created += batchResult.created;
        results.updated += batchResult.updated;
        results.skipped += batchResult.skipped;
        results.errors.push(...batchResult.errors);
        
      } catch (error) {
        console.error(`Error processing batch ${i}-${i + batchSize}:`, error);
        results.errors.push({
          index: i,
          error: `Batch processing failed: ${error}`,
        });
      }
    }

    return results;
  },
});

/**
 * Internal mutation to process a batch of products
 * PERFORMANCE: Efficient batch processing with proper error handling
 */
export const processBatch = internalMutation({
  args: {
    products: v.array(v.any()),
    startIndex: v.number(),
    skipExisting: v.boolean(),
  },
  handler: async (ctx, { products, startIndex, skipExisting }) => {
    const results = {
      processed: 0,
      created: 0,
      updated: 0,
      skipped: 0,
      errors: [] as Array<{ index: number; error: string; productId?: string }>,
    };

    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      const globalIndex = startIndex + i;

      try {
        // Validate required fields
        if (!product.title?.trim()) {
          results.errors.push({ index: globalIndex, error: "Title is required" });
          continue;
        }

        if (!product.supplierName?.trim()) {
          results.errors.push({ index: globalIndex, error: "Supplier name is required" });
          continue;
        }

        if (!product.providerId?.trim()) {
          results.errors.push({ index: globalIndex, error: "Provider ID is required" });
          continue;
        }

        // Check if product already exists
        if (skipExisting) {
          const existing = await ctx.db
            .query("providerData")
            .withIndex("by_provider", q => 
              q.eq("source", "alibaba").eq("providerId", product.providerId)
            )
            .first();

          if (existing) {
            results.skipped++;
            continue;
          }
        }

        // Find or create supplier
        let supplier = await ctx.db
          .query("suppliers")
          .filter(q => q.eq(q.field("name"), product.supplierName.trim()))
          .first();

        if (!supplier) {
          const supplierId = await ctx.db.insert("suppliers", {
            name: product.supplierName.trim(),
            contactInfo: {
              email: product.supplierContact?.email?.trim(),
              phone: product.supplierContact?.phone?.trim(),
              address: product.supplierContact?.address?.trim(),
            },
            rating: product.supplierRating,
            isActive: true,
            createdBy: "system" as Id<"users">, // System import
            notes: "Auto-created from Alibaba import",
          });
          supplier = await ctx.db.get(supplierId);
        }

        if (!supplier) {
          results.errors.push({ index: globalIndex, error: "Failed to create supplier" });
          continue;
        }

        // Process images - prioritize reliable sources
        const images: string[] = [];
        if (product.productImagePreviews?.length) {
          images.push(...product.productImagePreviews.filter((img: string) => img?.trim()));
        }
        if (product.productImageDescriptions?.length) {
          images.push(...product.productImageDescriptions.filter((img: string) => img?.trim()));
        }

        // Calculate final price from base price or first quantity offer
        let finalPrice = product.basePrice || 0;
        if (product.quantityOffers?.length && finalPrice === 0) {
          const firstOffer = product.quantityOffers[0];
          finalPrice = firstOffer.price;
        }

        // Create curation notes based on data quality
        const curationNotes = generateCurationNotes(product);

        // Create the main product
        const productId = await ctx.db.insert("products", {
          title: product.title.trim(),
          description: product.description?.trim() || "Imported from Alibaba",
          curationNotes,
          supplierId: supplier._id,
          basePrice: product.basePrice || finalPrice,
          currency: product.currency || "USD",
          finalPrice,
          tags: product.tags?.filter((tag: string) => tag?.trim()) || [],
          images: images.slice(0, 10), // Limit to 10 images
          stockCount: product.stockCount || 0,
          status: "inactive", // Start as inactive for manual review
          createdBy: "system" as Id<"users">, // System import
          updatedBy: "system" as Id<"users">,
          providerId: product.providerId,
          providerUrl: product.providerUrl,
          lastScraped: Date.now(),
          supplier: {
            id: supplier._id,
            name: supplier.name,
          },
        });

        // Create pricing tiers from quantity offers
        if (product.quantityOffers?.length) {
          await Promise.all(
            product.quantityOffers.map((offer: any) =>
              ctx.db.insert("productPricingTiers", {
                productId,
                minQuantity: offer.minQuantity,
                maxQuantity: offer.maxQuantity,
                price: offer.price,
                currency: offer.currency || "USD",
                isActive: true,
              })
            )
          );
        }

        // Create variants if provided
        if (product.variants?.length) {
          await Promise.all(
            product.variants.map((variant: any) =>
              ctx.db.insert("productVariants", {
                productId,
                type: variant.type,
                name: variant.name,
                value: variant.value,
                priceType: variant.priceModifier ? "modifier" : "absolute",
                priceModifier: variant.priceModifier,
                images: variant.images?.slice(0, 5) || [],
                isActive: true,
              })
            )
          );
        }

        // Create attributes if provided
        if (product.attributes?.length) {
          await Promise.all(
            product.attributes.map((attr: any) =>
              ctx.db.insert("productAttributes", {
                productId,
                name: attr.name,
                value: attr.value,
                type: attr.type || "string",
              })
            )
          );
        }

        // Store provider data for reference
        await ctx.db.insert("providerData", {
          productId,
          source: "alibaba",
          providerId: product.providerId,
          providerUrl: product.providerUrl,
          lastScraped: Date.now(),
          rawData: product.rawScrapedData,
          isActive: true,
        });

        // Update unique tags
        if (product.tags?.length) {
          const existingTags = await ctx.db
            .query("uniqueTags")
            .withIndex("by_tag")
            .collect();
          
          const existingTagSet = new Set(existingTags.map(t => t.tag));
          const newTags = product.tags.filter((tag: string) => 
            tag?.trim() && !existingTagSet.has(tag.trim())
          );
          
          await Promise.all(
            newTags.map((tag: string) =>
              ctx.db.insert("uniqueTags", { tag: tag.trim() })
            )
          );
        }

        results.created++;
        results.processed++;

      } catch (error) {
        console.error(`Error processing product at index ${globalIndex}:`, error);
        results.errors.push({
          index: globalIndex,
          error: `Processing failed: ${error}`,
          productId: product.providerId,
        });
      }
    }

    return results;
  },
});

/**
 * Generate curation notes based on data quality
 * Helps curators understand what needs attention
 */
function generateCurationNotes(product: any): string {
  const notes: string[] = [];
  
  // Check image quality
  const totalImages = (product.productImagePreviews?.length || 0) + 
                     (product.productImageDescriptions?.length || 0);
  if (totalImages === 0) {
    notes.push("⚠️ No images available");
  } else if (totalImages < 3) {
    notes.push("📸 Limited images - consider adding more");
  }
  
  // Check pricing data
  if (!product.basePrice && !product.quantityOffers?.length) {
    notes.push("💰 No pricing data available");
  }
  
  // Check description quality
  if (!product.description || product.description.length < 50) {
    notes.push("📝 Description needs improvement");
  }
  
  // Check supplier rating
  if (!product.supplierRating) {
    notes.push("⭐ Supplier rating unknown");
  } else if (product.supplierRating < 3) {
    notes.push("⚠️ Low supplier rating - verify quality");
  }
  
  // Check stock information
  if (!product.stockCount) {
    notes.push("📦 Stock count unknown");
  }
  
  if (notes.length === 0) {
    notes.push("✅ Good data quality - ready for review");
  }
  
  return notes.join(" | ");
}

/**
 * Update existing products with new scraped data
 * PERFORMANCE: Efficient updates with change detection
 */
export const updateProductsFromScrape = action({
  args: {
    updates: v.array(v.object({
      providerId: v.string(),
      basePrice: v.optional(v.number()),
      stockCount: v.optional(v.number()),
      images: v.optional(v.array(v.string())),
      quantityOffers: v.optional(v.array(v.object({
        minQuantity: v.number(),
        price: v.number(),
        currency: v.string(),
      }))),
      rawScrapedData: v.optional(v.any()),
    })),
  },
  handler: async (ctx, { updates }) => {
    const results = {
      total: updates.length,
      updated: 0,
      notFound: 0,
      errors: [] as Array<{ providerId: string; error: string }>,
    };

    for (const update of updates) {
      try {
        const result = await ctx.runMutation(internal.dataImport.updateSingleProduct, {
          providerId: update.providerId,
          updates: update,
        });

        if (result.found) {
          results.updated++;
        } else {
          results.notFound++;
        }
      } catch (error) {
        results.errors.push({
          providerId: update.providerId,
          error: `Update failed: ${error}`,
        });
      }
    }

    return results;
  },
});

/**
 * Internal mutation to update a single product
 */
export const updateSingleProduct = internalMutation({
  args: {
    providerId: v.string(),
    updates: v.any(),
  },
  handler: async (ctx, { providerId, updates }) => {
    // Find the product by provider ID
    const providerData = await ctx.db
      .query("providerData")
      .withIndex("by_provider", q =>
        q.eq("source", "alibaba").eq("providerId", providerId)
      )
      .first();

    if (!providerData) {
      return { found: false };
    }

    const product = await ctx.db.get(providerData.productId);
    if (!product) {
      return { found: false };
    }

    // Prepare product updates
    const productUpdates: Partial<Doc<"products">> = {
      lastScraped: Date.now(),
      updatedBy: "system" as Id<"users">,
    };

    if (updates.basePrice !== undefined) {
      productUpdates.basePrice = updates.basePrice;
      productUpdates.finalPrice = updates.basePrice;
    }

    if (updates.stockCount !== undefined) {
      productUpdates.stockCount = updates.stockCount;
    }

    if (updates.images?.length) {
      productUpdates.images = updates.images.slice(0, 10);
    }

    // Update the product
    await ctx.db.patch(product._id, productUpdates);

    // Update pricing tiers if provided
    if (updates.quantityOffers?.length) {
      // Deactivate existing tiers
      const existingTiers = await ctx.db
        .query("productPricingTiers")
        .withIndex("by_product", q => q.eq("productId", product._id))
        .collect();

      await Promise.all(
        existingTiers.map(tier =>
          ctx.db.patch(tier._id, { isActive: false })
        )
      );

      // Create new tiers
      await Promise.all(
        updates.quantityOffers.map((offer: any) =>
          ctx.db.insert("productPricingTiers", {
            productId: product._id,
            minQuantity: offer.minQuantity,
            maxQuantity: offer.maxQuantity,
            price: offer.price,
            currency: offer.currency || "USD",
            isActive: true,
          })
        )
      );
    }

    // Update provider data
    await ctx.db.patch(providerData._id, {
      lastScraped: Date.now(),
      rawData: updates.rawScrapedData,
    });

    return { found: true };
  },
});

/**
 * Get import statistics and status
 * PERFORMANCE: Efficient aggregation of import data
 */
export const getImportStats = internalQuery({
  args: {
    dateRange: v.optional(v.object({
      start: v.number(),
      end: v.number(),
    })),
  },
  handler: async (ctx, { dateRange }) => {
    // Get all provider data
    const allProviderData = await ctx.db
      .query("providerData")
      .withIndex("by_active", q => q.eq("isActive", true))
      .collect();

    let filteredData = allProviderData;
    if (dateRange) {
      filteredData = allProviderData.filter(data =>
        data.lastScraped >= dateRange.start &&
        data.lastScraped <= dateRange.end
      );
    }

    // Get all products to calculate import success rate
    const allProducts = await ctx.db.query("products").collect();
    const importedProducts = allProducts.filter(p => p.providerId);

    const stats = {
      totalImported: filteredData.length,
      bySource: {
        alibaba: filteredData.filter(d => d.source === "alibaba").length,
      },
      productStats: {
        totalProducts: allProducts.length,
        importedProducts: importedProducts.length,
        manualProducts: allProducts.length - importedProducts.length,
        activeImported: importedProducts.filter(p => p.status === "active").length,
        inactiveImported: importedProducts.filter(p => p.status === "inactive").length,
      },
      lastImportDate: filteredData.length > 0
        ? Math.max(...filteredData.map(d => d.lastScraped))
        : 0,
      oldestImportDate: filteredData.length > 0
        ? Math.min(...filteredData.map(d => d.lastScraped))
        : 0,
    };

    return stats;
  },
});

/**
 * Clean up old or invalid import data
 * PERFORMANCE: Batch cleanup operations
 */
export const cleanupImportData = mutation({
  args: {
    olderThanDays: v.optional(v.number()),
    removeInactive: v.optional(v.boolean()),
  },
  handler: async (ctx, { olderThanDays = 90, removeInactive = false }) => {
    await requirePermission(ctx, PERMISSIONS.SYSTEM_SETTINGS);

    const cutoffDate = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);

    // Find old provider data
    const oldProviderData = await ctx.db
      .query("providerData")
      .filter(q => q.lt(q.field("lastScraped"), cutoffDate))
      .collect();

    // Find inactive provider data if requested
    let inactiveProviderData: any[] = [];
    if (removeInactive) {
      inactiveProviderData = await ctx.db
        .query("providerData")
        .withIndex("by_active", q => q.eq("isActive", false))
        .collect();
    }

    const toDelete = [...oldProviderData, ...inactiveProviderData];

    // Delete the data
    await Promise.all(
      toDelete.map(data => ctx.db.delete(data._id))
    );

    return {
      deletedCount: toDelete.length,
      oldDataDeleted: oldProviderData.length,
      inactiveDataDeleted: inactiveProviderData.length,
    };
  },
});
