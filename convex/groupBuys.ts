import { v } from "convex/values";
import { query, mutation, internalQuery, internalMutation } from "./_generated/server";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Doc } from "./_generated/dataModel";

// ============================================================================
// GROUP BUY QUERIES
// ============================================================================

/**
 * Get active group buys with filtering and pagination
 * PERFORMANCE: Uses proper indexes and efficient filtering
 */
export const getGroupBuys = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    filters: v.optional(v.object({
      status: v.optional(v.union(v.literal("active"), v.literal("completed"), v.literal("expired"))),
      productId: v.optional(v.id("products")),
      createdBy: v.optional(v.id("users")),
    })),
    sortBy: v.optional(v.union(
      v.literal("_creationTime"),
      v.literal("endTime"),
      v.literal("currentParticipants")
    )),
    sortOrder: v.optional(v.union(v.literal("asc"), v.literal("desc"))),
  },
  handler: async (ctx, { paginationOpts, filters, sortBy = "endTime", sortOrder = "asc" }) => {
    await requirePermission(ctx, PERMISSIONS.GROUP_BUYS_VIEW);

    // Build query with proper index usage
    let query = ctx.db.query("groupBuys");

    // Apply filters using indexes where possible
    if (filters?.status) {
      query = query.withIndex("by_status", q => q.eq("status", filters.status));
    } else if (filters?.productId) {
      query = query.withIndex("by_product", q => q.eq("productId", filters.productId));
    } else {
      // Default to active group buys
      query = query.withIndex("by_status", q => q.eq("status", "active"));
    }

    // Apply sorting
    if (sortOrder === "desc") {
      query = query.order("desc");
    }

    // Get paginated results
    const paginationOptions = paginationOpts || { numItems: 20, cursor: null };
    const results = await query.paginate(paginationOptions);

    // Apply additional filters in code
    let filteredGroupBuys = results.page;

    if (filters?.createdBy) {
      filteredGroupBuys = filteredGroupBuys.filter(gb => gb.createdBy === filters.createdBy);
    }

    // Enrich with product and creator info
    const enrichedGroupBuys = await Promise.all(
      filteredGroupBuys.map(async (groupBuy) => {
        const [product, creator, participantsCount] = await Promise.all([
          ctx.db.get(groupBuy.productId),
          ctx.db.get(groupBuy.createdBy),
          ctx.db
            .query("groupBuyParticipants")
            .withIndex("by_group_buy", q => q.eq("groupBuyId", groupBuy._id))
            .collect()
            .then(participants => participants.length),
        ]);

        // Calculate progress to next tier
        const sortedTiers = groupBuy.targetTiers.sort((a, b) => a.quantity - b.quantity);
        const nextTier = sortedTiers.find(tier => tier.quantity > groupBuy.currentParticipants);
        const currentTier = sortedTiers
          .filter(tier => tier.quantity <= groupBuy.currentParticipants)
          .pop();

        // Calculate time remaining
        const now = Date.now();
        const timeRemaining = Math.max(0, groupBuy.endTime - now);
        const isExpired = timeRemaining === 0;

        return {
          ...groupBuy,
          product: product ? {
            id: product._id,
            title: product.title,
            images: product.images.slice(0, 1),
            basePrice: product.finalPrice,
          } : null,
          creator: creator ? {
            id: creator._id,
            name: creator.name,
          } : null,
          actualParticipants: participantsCount,
          currentTier,
          nextTier,
          progressToNextTier: nextTier 
            ? (groupBuy.currentParticipants / nextTier.quantity) * 100 
            : 100,
          timeRemaining,
          isExpired,
          daysRemaining: Math.ceil(timeRemaining / (24 * 60 * 60 * 1000)),
        };
      })
    );

    return {
      ...results,
      page: enrichedGroupBuys,
      totalFiltered: filteredGroupBuys.length,
      hasFilters: !!(filters && Object.keys(filters).length > 0),
    };
  },
});

/**
 * Get a single group buy with full details
 * PERFORMANCE: Loads all related data efficiently
 */
export const getGroupBuy = query({
  args: { id: v.id("groupBuys") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.GROUP_BUYS_VIEW);

    const groupBuy = await ctx.db.get(id);
    if (!groupBuy) {
      throw new Error("Group buy not found");
    }

    // Get related data in parallel
    const [product, creator, participants] = await Promise.all([
      ctx.db.get(groupBuy.productId),
      ctx.db.get(groupBuy.createdBy),
      ctx.db
        .query("groupBuyParticipants")
        .withIndex("by_group_buy", q => q.eq("groupBuyId", id))
        .collect(),
    ]);

    // Get participant user details
    const participantUsers = await Promise.all(
      participants.map(async (participant) => {
        const user = await ctx.db.get(participant.userId);
        return {
          ...participant,
          user: user ? {
            id: user._id,
            name: user.name,
          } : null,
        };
      })
    );

    // Calculate tier information
    const sortedTiers = groupBuy.targetTiers.sort((a, b) => a.quantity - b.quantity);
    const currentTier = sortedTiers
      .filter(tier => tier.quantity <= groupBuy.currentParticipants)
      .pop();
    const nextTier = sortedTiers.find(tier => tier.quantity > groupBuy.currentParticipants);

    // Calculate time remaining
    const now = Date.now();
    const timeRemaining = Math.max(0, groupBuy.endTime - now);

    return {
      ...groupBuy,
      product,
      creator: creator ? {
        id: creator._id,
        name: creator.name,
      } : null,
      participants: participantUsers.sort((a, b) => a.joinedAt - b.joinedAt),
      currentTier,
      nextTier,
      allTiers: sortedTiers,
      timeRemaining,
      isExpired: timeRemaining === 0,
      daysRemaining: Math.ceil(timeRemaining / (24 * 60 * 60 * 1000)),
      hoursRemaining: Math.ceil(timeRemaining / (60 * 60 * 1000)),
      totalQuantity: participants.reduce((sum, p) => sum + p.quantity, 0),
      averageQuantityPerParticipant: participants.length > 0 
        ? participants.reduce((sum, p) => sum + p.quantity, 0) / participants.length 
        : 0,
    };
  },
});

/**
 * Get group buys for the current user
 * PERFORMANCE: Uses user-specific filtering
 */
export const getMyGroupBuys = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    type: v.optional(v.union(v.literal("created"), v.literal("participating"))),
  },
  handler: async (ctx, { paginationOpts, type }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const paginationOptions = paginationOpts || { numItems: 10, cursor: null };

    if (type === "created") {
      // Get group buys created by the user
      const results = await ctx.db
        .query("groupBuys")
        .filter(q => q.eq(q.field("createdBy"), userId))
        .order("desc")
        .paginate(paginationOptions);

      const enrichedGroupBuys = await Promise.all(
        results.page.map(async (groupBuy) => {
          const [product, participantsCount] = await Promise.all([
            ctx.db.get(groupBuy.productId),
            ctx.db
              .query("groupBuyParticipants")
              .withIndex("by_group_buy", q => q.eq("groupBuyId", groupBuy._id))
              .collect()
              .then(participants => participants.length),
          ]);

          return {
            ...groupBuy,
            product: product ? {
              id: product._id,
              title: product.title,
              images: product.images.slice(0, 1),
            } : null,
            actualParticipants: participantsCount,
            timeRemaining: Math.max(0, groupBuy.endTime - Date.now()),
          };
        })
      );

      return {
        ...results,
        page: enrichedGroupBuys,
      };
    } else {
      // Get group buys the user is participating in
      const participations = await ctx.db
        .query("groupBuyParticipants")
        .filter(q => q.eq(q.field("userId"), userId))
        .collect();

      const groupBuyIds = participations.map(p => p.groupBuyId);
      const groupBuys = await Promise.all(
        groupBuyIds.map(id => ctx.db.get(id))
      );

      const validGroupBuys = groupBuys.filter(gb => gb !== null) as Doc<"groupBuys">[];

      const enrichedGroupBuys = await Promise.all(
        validGroupBuys.map(async (groupBuy) => {
          const product = await ctx.db.get(groupBuy.productId);
          const myParticipation = participations.find(p => p.groupBuyId === groupBuy._id);

          return {
            ...groupBuy,
            product: product ? {
              id: product._id,
              title: product.title,
              images: product.images.slice(0, 1),
            } : null,
            myQuantity: myParticipation?.quantity || 0,
            myJoinedAt: myParticipation?.joinedAt || 0,
            timeRemaining: Math.max(0, groupBuy.endTime - Date.now()),
          };
        })
      );

      // Sort by join date (most recent first)
      enrichedGroupBuys.sort((a, b) => b.myJoinedAt - a.myJoinedAt);

      return {
        page: enrichedGroupBuys,
        isDone: true,
        continueCursor: null,
      };
    }
  },
});

/**
 * Check if user is participating in a group buy
 * PERFORMANCE: Simple index lookup
 */
export const isParticipating = query({
  args: { groupBuyId: v.id("groupBuys") },
  handler: async (ctx, { groupBuyId }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return false;
    }

    const participation = await ctx.db
      .query("groupBuyParticipants")
      .withIndex("by_group_buy_and_user", q => 
        q.eq("groupBuyId", groupBuyId).eq("userId", userId)
      )
      .first();

    return {
      isParticipating: !!participation,
      quantity: participation?.quantity || 0,
      joinedAt: participation?.joinedAt || 0,
    };
  },
});

// ============================================================================
// GROUP BUY MUTATIONS
// ============================================================================

/**
 * Create a new group buy
 * PERFORMANCE: Validates product and creates with proper structure
 */
export const createGroupBuy = mutation({
  args: {
    productId: v.id("products"),
    targetTiers: v.array(v.object({
      quantity: v.number(),
      price: v.number(),
    })),
    durationDays: v.number(),
    providerPricingTiers: v.array(v.object({
      minParticipants: v.number(),
      pricePerUnit: v.number(),
      ourCommission: v.number(),
      finalPrice: v.number(),
    })),
  },
  handler: async (ctx, { productId, targetTiers, durationDays, providerPricingTiers }) => {
    await requirePermission(ctx, PERMISSIONS.GROUP_BUYS_CREATE);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate product exists and is active
    const product = await ctx.db.get(productId);
    if (!product) {
      throw new Error("Product not found");
    }

    if (product.status !== "active") {
      throw new Error("Cannot create group buy for inactive product");
    }

    // Validate target tiers
    if (targetTiers.length === 0) {
      throw new Error("At least one target tier is required");
    }

    const sortedTiers = targetTiers.sort((a, b) => a.quantity - b.quantity);
    for (let i = 0; i < sortedTiers.length; i++) {
      if (sortedTiers[i].quantity <= 0) {
        throw new Error("Target quantities must be positive");
      }
      if (sortedTiers[i].price <= 0) {
        throw new Error("Target prices must be positive");
      }
      if (i > 0 && sortedTiers[i].price >= sortedTiers[i - 1].price) {
        throw new Error("Higher quantity tiers must have lower prices");
      }
    }

    // Validate duration
    if (durationDays <= 0 || durationDays > 90) {
      throw new Error("Duration must be between 1 and 90 days");
    }

    // Check if there's already an active group buy for this product
    const existingGroupBuy = await ctx.db
      .query("groupBuys")
      .withIndex("by_product", q => q.eq("productId", productId))
      .filter(q => q.eq(q.field("status"), "active"))
      .first();

    if (existingGroupBuy) {
      throw new Error("There is already an active group buy for this product");
    }

    // Calculate end time
    const startTime = Date.now();
    const endTime = startTime + (durationDays * 24 * 60 * 60 * 1000);

    // Create the group buy
    const groupBuyId = await ctx.db.insert("groupBuys", {
      productId,
      targetTiers: sortedTiers,
      currentParticipants: 0,
      status: "active",
      startTime,
      endTime,
      createdBy: userId,
      providerPricingTiers,
    });

    return { groupBuyId, success: true };
  },
});

/**
 * Join a group buy
 * PERFORMANCE: Atomic updates with proper validation
 */
export const joinGroupBuy = mutation({
  args: {
    groupBuyId: v.id("groupBuys"),
    quantity: v.number(),
  },
  handler: async (ctx, { groupBuyId, quantity }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const groupBuy = await ctx.db.get(groupBuyId);
    if (!groupBuy) {
      throw new Error("Group buy not found");
    }

    // Validate group buy is active
    if (groupBuy.status !== "active") {
      throw new Error("Group buy is not active");
    }

    // Check if not expired
    if (Date.now() > groupBuy.endTime) {
      throw new Error("Group buy has expired");
    }

    // Validate quantity
    if (quantity <= 0) {
      throw new Error("Quantity must be positive");
    }

    // Check if user is already participating
    const existingParticipation = await ctx.db
      .query("groupBuyParticipants")
      .withIndex("by_group_buy_and_user", q =>
        q.eq("groupBuyId", groupBuyId).eq("userId", userId)
      )
      .first();

    if (existingParticipation) {
      throw new Error("You are already participating in this group buy");
    }

    // Add participant
    await ctx.db.insert("groupBuyParticipants", {
      groupBuyId,
      userId,
      quantity,
      joinedAt: Date.now(),
    });

    // Update current participants count
    await ctx.db.patch(groupBuyId, {
      currentParticipants: groupBuy.currentParticipants + 1,
    });

    return { success: true };
  },
});

/**
 * Leave a group buy
 * PERFORMANCE: Atomic updates with cleanup
 */
export const leaveGroupBuy = mutation({
  args: { groupBuyId: v.id("groupBuys") },
  handler: async (ctx, { groupBuyId }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const [groupBuy, participation] = await Promise.all([
      ctx.db.get(groupBuyId),
      ctx.db
        .query("groupBuyParticipants")
        .withIndex("by_group_buy_and_user", q =>
          q.eq("groupBuyId", groupBuyId).eq("userId", userId)
        )
        .first(),
    ]);

    if (!groupBuy) {
      throw new Error("Group buy not found");
    }

    if (!participation) {
      throw new Error("You are not participating in this group buy");
    }

    // Can only leave if group buy is still active
    if (groupBuy.status !== "active") {
      throw new Error("Cannot leave a completed or expired group buy");
    }

    // Remove participation
    await ctx.db.delete(participation._id);

    // Update current participants count
    await ctx.db.patch(groupBuyId, {
      currentParticipants: Math.max(0, groupBuy.currentParticipants - 1),
    });

    return { success: true };
  },
});

/**
 * Update participant quantity in a group buy
 * PERFORMANCE: Simple update with validation
 */
export const updateGroupBuyQuantity = mutation({
  args: {
    groupBuyId: v.id("groupBuys"),
    quantity: v.number(),
  },
  handler: async (ctx, { groupBuyId, quantity }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const [groupBuy, participation] = await Promise.all([
      ctx.db.get(groupBuyId),
      ctx.db
        .query("groupBuyParticipants")
        .withIndex("by_group_buy_and_user", q =>
          q.eq("groupBuyId", groupBuyId).eq("userId", userId)
        )
        .first(),
    ]);

    if (!groupBuy) {
      throw new Error("Group buy not found");
    }

    if (!participation) {
      throw new Error("You are not participating in this group buy");
    }

    if (groupBuy.status !== "active") {
      throw new Error("Cannot update quantity for inactive group buy");
    }

    if (quantity <= 0) {
      throw new Error("Quantity must be positive");
    }

    await ctx.db.patch(participation._id, { quantity });

    return { success: true };
  },
});

/**
 * Complete a group buy (admin only)
 * PERFORMANCE: Transitions status and creates orders efficiently
 */
export const completeGroupBuy = mutation({
  args: { groupBuyId: v.id("groupBuys") },
  handler: async (ctx, { groupBuyId }) => {
    await requirePermission(ctx, PERMISSIONS.GROUP_BUYS_EDIT);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const groupBuy = await ctx.db.get(groupBuyId);
    if (!groupBuy) {
      throw new Error("Group buy not found");
    }

    if (groupBuy.status !== "active") {
      throw new Error("Group buy is not active");
    }

    // Check if minimum tier is reached
    const sortedTiers = groupBuy.targetTiers.sort((a, b) => a.quantity - b.quantity);
    const minTier = sortedTiers[0];

    if (groupBuy.currentParticipants < minTier.quantity) {
      throw new Error("Minimum participation not reached");
    }

    // Update status to completed
    await ctx.db.patch(groupBuyId, {
      status: "completed",
    });

    return { success: true };
  },
});

/**
 * Expire a group buy (system function)
 * PERFORMANCE: Batch updates for expired group buys
 */
export const expireGroupBuy = mutation({
  args: { groupBuyId: v.id("groupBuys") },
  handler: async (ctx, { groupBuyId }) => {
    await requirePermission(ctx, PERMISSIONS.GROUP_BUYS_EDIT);

    const groupBuy = await ctx.db.get(groupBuyId);
    if (!groupBuy) {
      throw new Error("Group buy not found");
    }

    if (groupBuy.status !== "active") {
      throw new Error("Group buy is not active");
    }

    // Check if actually expired
    if (Date.now() <= groupBuy.endTime) {
      throw new Error("Group buy has not expired yet");
    }

    // Update status to expired
    await ctx.db.patch(groupBuyId, {
      status: "expired",
    });

    return { success: true };
  },
});

/**
 * Get group buy statistics
 * PERFORMANCE: Efficient aggregation queries
 */
export const getGroupBuyStats = query({
  args: {
    dateRange: v.optional(v.object({
      start: v.number(),
      end: v.number(),
    })),
  },
  handler: async (ctx, { dateRange }) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    // Get all group buys
    const allGroupBuys = await ctx.db.query("groupBuys").collect();

    let filteredGroupBuys = allGroupBuys;
    if (dateRange) {
      filteredGroupBuys = allGroupBuys.filter(gb =>
        gb._creationTime >= dateRange.start &&
        gb._creationTime <= dateRange.end
      );
    }

    // Calculate statistics
    const stats = {
      total: filteredGroupBuys.length,
      byStatus: {
        active: filteredGroupBuys.filter(gb => gb.status === "active").length,
        completed: filteredGroupBuys.filter(gb => gb.status === "completed").length,
        expired: filteredGroupBuys.filter(gb => gb.status === "expired").length,
      },
      totalParticipants: filteredGroupBuys.reduce((sum, gb) => sum + gb.currentParticipants, 0),
      averageParticipants: 0,
      completionRate: 0,
      mostPopularProducts: [] as Array<{ productId: string; count: number; title?: string }>,
    };

    if (filteredGroupBuys.length > 0) {
      stats.averageParticipants = stats.totalParticipants / filteredGroupBuys.length;

      const completedOrExpired = stats.byStatus.completed + stats.byStatus.expired;
      if (completedOrExpired > 0) {
        stats.completionRate = (stats.byStatus.completed / completedOrExpired) * 100;
      }

      // Calculate most popular products
      const productCounts = new Map<string, number>();
      filteredGroupBuys.forEach(gb => {
        const count = productCounts.get(gb.productId) || 0;
        productCounts.set(gb.productId, count + 1);
      });

      const sortedProducts = Array.from(productCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5);

      stats.mostPopularProducts = await Promise.all(
        sortedProducts.map(async ([productId, count]) => {
          const product = await ctx.db.get(productId as any);
          return {
            productId,
            count,
            title: (product as any)?.title,
          };
        })
      );
    }

    return stats;
  },
});
