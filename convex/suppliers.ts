import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Doc } from "./_generated/dataModel";

// ============================================================================
// SUPPLIER QUERIES
// ============================================================================

/**
 * Get suppliers with filtering, search, and pagination
 * PERFORMANCE: Uses proper indexes and pagination
 */
export const getSuppliers = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    filters: v.optional(v.object({
      isActive: v.optional(v.boolean()),
      hasProducts: v.optional(v.boolean()),
      minRating: v.optional(v.number()),
    })),
    search: v.optional(v.string()),
    sortBy: v.optional(v.union(
      v.literal("name"),
      v.literal("rating"),
      v.literal("_creationTime")
    )),
    sortOrder: v.optional(v.union(v.literal("asc"), v.literal("desc"))),
  },
  handler: async (ctx, { paginationOpts, filters, search, sortBy = "name", sortOrder = "asc" }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_VIEW);

    // Build query with proper index usage
    let query = ctx.db.query("suppliers");

    // Apply filters using indexes where possible
    if (filters?.isActive !== undefined) {
      query = query.withIndex("by_active", q => q.eq("isActive", filters.isActive));
    }

    // Apply sorting
    if (sortOrder === "desc") {
      query = query.order("desc");
    }

    // Get paginated results
    const paginationOptions = paginationOpts || { numItems: 20, cursor: null };
    const results = await query.paginate(paginationOptions);

    // Apply additional filters in code
    let filteredSuppliers = results.page;

    if (filters?.minRating) {
      filteredSuppliers = filteredSuppliers.filter(supplier =>
        supplier.rating && supplier.rating >= filters.minRating!
      );
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredSuppliers = filteredSuppliers.filter(supplier =>
        supplier.name.toLowerCase().includes(searchLower) ||
        supplier.contactInfo.email?.toLowerCase().includes(searchLower) ||
        supplier.notes?.toLowerCase().includes(searchLower)
      );
    }

    // Enrich suppliers with product counts and creator info
    const enrichedSuppliers = await Promise.all(
      filteredSuppliers.map(async (supplier) => {
        const [creator, productCounts] = await Promise.all([
          ctx.db.get(supplier.createdBy),
          ctx.db
            .query("products")
            .withIndex("by_supplier", q => q.eq("supplierId", supplier._id))
            .collect()
            .then(products => ({
              total: products.length,
              active: products.filter(p => p.status === "active").length,
              inactive: products.filter(p => p.status === "inactive").length,
              archived: products.filter(p => p.status === "archived").length,
            })),
        ]);

        return {
          ...supplier,
          creator: creator ? {
            id: creator._id,
            name: creator.name,
          } : null,
          productCounts,
          hasProducts: productCounts.total > 0,
        };
      })
    );

    // Apply hasProducts filter after enrichment
    if (filters?.hasProducts !== undefined) {
      const finalFiltered = enrichedSuppliers.filter(supplier =>
        filters.hasProducts ? supplier.hasProducts : !supplier.hasProducts
      );
      
      return {
        ...results,
        page: finalFiltered,
        totalFiltered: finalFiltered.length,
        hasFilters: !!(filters && Object.keys(filters).length > 0),
        hasSearch: !!search?.trim(),
      };
    }

    return {
      ...results,
      page: enrichedSuppliers,
      totalFiltered: filteredSuppliers.length,
      hasFilters: !!(filters && Object.keys(filters).length > 0),
      hasSearch: !!search?.trim(),
    };
  },
});

/**
 * Get a single supplier with full details
 * PERFORMANCE: Loads all related data efficiently
 */
export const getSupplier = query({
  args: { id: v.id("suppliers") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_VIEW);

    const supplier = await ctx.db.get(id);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Get related data in parallel
    const [creator, products, recentProducts] = await Promise.all([
      ctx.db.get(supplier.createdBy),
      ctx.db
        .query("products")
        .withIndex("by_supplier", q => q.eq("supplierId", id))
        .collect(),
      ctx.db
        .query("products")
        .withIndex("by_supplier", q => q.eq("supplierId", id))
        .order("desc")
        .take(5),
    ]);

    // Calculate statistics
    const productStats = {
      total: products.length,
      active: products.filter(p => p.status === "active").length,
      inactive: products.filter(p => p.status === "inactive").length,
      archived: products.filter(p => p.status === "archived").length,
      averagePrice: products.length > 0 
        ? products.reduce((sum, p) => sum + p.finalPrice, 0) / products.length 
        : 0,
      totalValue: products
        .filter(p => p.status === "active")
        .reduce((sum, p) => sum + (p.finalPrice * p.stockCount), 0),
    };

    // Get orders statistics for this supplier's products
    const allOrders = await ctx.db.query("orders").collect();
    const supplierOrders = allOrders.filter(order =>
      order.items.some(item => 
        products.some(product => product._id === item.productId)
      )
    );

    const orderStats = {
      totalOrders: supplierOrders.length,
      totalRevenue: supplierOrders
        .filter(order => order.status === "delivered")
        .reduce((sum, order) => sum + order.totalAmount, 0),
      averageOrderValue: supplierOrders.length > 0
        ? supplierOrders.reduce((sum, order) => sum + order.totalAmount, 0) / supplierOrders.length
        : 0,
    };

    return {
      ...supplier,
      creator: creator ? {
        id: creator._id,
        name: creator.name,
        email: creator.email,
      } : null,
      productStats,
      orderStats,
      recentProducts: recentProducts.map(product => ({
        id: product._id,
        title: product.title,
        finalPrice: product.finalPrice,
        status: product.status,
        stockCount: product.stockCount,
        createdAt: product._creationTime,
      })),
    };
  },
});

/**
 * Get supplier options for dropdowns
 * PERFORMANCE: Returns minimal data for UI components
 */
export const getSupplierOptions = query({
  args: {
    activeOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, { activeOnly = true }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_VIEW);

    let query = ctx.db.query("suppliers");
    
    if (activeOnly) {
      query = query.withIndex("by_active", q => q.eq("isActive", true));
    }

    const suppliers = await query.collect();

    return suppliers
      .sort((a, b) => a.name.localeCompare(b.name))
      .map(supplier => ({
        id: supplier._id,
        name: supplier.name,
        rating: supplier.rating,
        isActive: supplier.isActive,
      }));
  },
});

/**
 * Get supplier statistics for dashboard
 * PERFORMANCE: Efficient aggregation queries
 */
export const getSupplierStats = query({
  args: {
    dateRange: v.optional(v.object({
      start: v.number(),
      end: v.number(),
    })),
  },
  handler: async (ctx, { dateRange }) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    // Get all suppliers
    const allSuppliers = await ctx.db.query("suppliers").collect();

    let filteredSuppliers = allSuppliers;
    if (dateRange) {
      filteredSuppliers = allSuppliers.filter(supplier =>
        supplier._creationTime >= dateRange.start &&
        supplier._creationTime <= dateRange.end
      );
    }

    // Get all products to calculate supplier performance
    const allProducts = await ctx.db.query("products").collect();

    const stats = {
      total: filteredSuppliers.length,
      active: filteredSuppliers.filter(s => s.isActive).length,
      inactive: filteredSuppliers.filter(s => !s.isActive).length,
      withProducts: 0,
      withoutProducts: 0,
      averageRating: 0,
      topPerformers: [] as Array<{
        id: string;
        name: string;
        productCount: number;
        rating?: number;
      }>,
    };

    // Calculate suppliers with/without products
    const supplierProductCounts = new Map<string, number>();
    allProducts.forEach(product => {
      const count = supplierProductCounts.get(product.supplierId) || 0;
      supplierProductCounts.set(product.supplierId, count + 1);
    });

    stats.withProducts = filteredSuppliers.filter(s => 
      supplierProductCounts.has(s._id)
    ).length;
    stats.withoutProducts = stats.total - stats.withProducts;

    // Calculate average rating
    const ratedSuppliers = filteredSuppliers.filter(s => s.rating !== undefined);
    if (ratedSuppliers.length > 0) {
      stats.averageRating = ratedSuppliers.reduce((sum, s) => sum + (s.rating || 0), 0) / ratedSuppliers.length;
    }

    // Get top performers (by product count and rating)
    stats.topPerformers = filteredSuppliers
      .map(supplier => ({
        id: supplier._id,
        name: supplier.name,
        productCount: supplierProductCounts.get(supplier._id) || 0,
        rating: supplier.rating,
      }))
      .sort((a, b) => {
        // Sort by product count first, then by rating
        if (a.productCount !== b.productCount) {
          return b.productCount - a.productCount;
        }
        return (b.rating || 0) - (a.rating || 0);
      })
      .slice(0, 5);

    return stats;
  },
});

// ============================================================================
// SUPPLIER MUTATIONS
// ============================================================================

/**
 * Create a new supplier
 * PERFORMANCE: Validates data and creates with proper structure
 */
export const createSupplier = mutation({
  args: {
    name: v.string(),
    contactInfo: v.object({
      email: v.optional(v.string()),
      phone: v.optional(v.string()),
      address: v.optional(v.string()),
    }),
    platformUrl: v.optional(v.string()),
    rating: v.optional(v.number()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_CREATE);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate required fields
    if (!args.name.trim()) {
      throw new Error("Supplier name is required");
    }

    // Validate email format if provided
    if (args.contactInfo.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(args.contactInfo.email)) {
        throw new Error("Invalid email format");
      }
    }

    // Validate rating if provided
    if (args.rating !== undefined && (args.rating < 0 || args.rating > 5)) {
      throw new Error("Rating must be between 0 and 5");
    }

    // Check if supplier with same name already exists
    const existingSupplier = await ctx.db
      .query("suppliers")
      .filter(q => q.eq(q.field("name"), args.name.trim()))
      .first();

    if (existingSupplier) {
      throw new Error("A supplier with this name already exists");
    }

    // Create the supplier
    const supplierId = await ctx.db.insert("suppliers", {
      name: args.name.trim(),
      contactInfo: {
        email: args.contactInfo.email?.trim(),
        phone: args.contactInfo.phone?.trim(),
        address: args.contactInfo.address?.trim(),
      },
      platformUrl: args.platformUrl?.trim(),
      rating: args.rating,
      notes: args.notes?.trim(),
      isActive: true,
      createdBy: userId,
    });

    return { supplierId, success: true };
  },
});

/**
 * Update an existing supplier
 * PERFORMANCE: Only updates changed fields
 */
export const updateSupplier = mutation({
  args: {
    id: v.id("suppliers"),
    name: v.optional(v.string()),
    contactInfo: v.optional(v.object({
      email: v.optional(v.string()),
      phone: v.optional(v.string()),
      address: v.optional(v.string()),
    })),
    platformUrl: v.optional(v.string()),
    rating: v.optional(v.number()),
    notes: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, { id, ...updates }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_EDIT);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const supplier = await ctx.db.get(id);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Validate updates
    if (updates.name !== undefined && !updates.name.trim()) {
      throw new Error("Supplier name cannot be empty");
    }

    if (updates.contactInfo?.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(updates.contactInfo.email)) {
        throw new Error("Invalid email format");
      }
    }

    if (updates.rating !== undefined && (updates.rating < 0 || updates.rating > 5)) {
      throw new Error("Rating must be between 0 and 5");
    }

    // Check for name conflicts if name is being updated
    if (updates.name && updates.name.trim() !== supplier.name) {
      const existingSupplier = await ctx.db
        .query("suppliers")
        .filter(q =>
          q.and(
            q.eq(q.field("name"), updates.name!.trim()),
            q.neq(q.field("_id"), id)
          )
        )
        .first();

      if (existingSupplier) {
        throw new Error("A supplier with this name already exists");
      }
    }

    // Prepare update object
    const updateData: Partial<Doc<"suppliers">> = {};

    if (updates.name !== undefined) updateData.name = updates.name.trim();
    if (updates.platformUrl !== undefined) updateData.platformUrl = updates.platformUrl?.trim();
    if (updates.rating !== undefined) updateData.rating = updates.rating;
    if (updates.notes !== undefined) updateData.notes = updates.notes?.trim();
    if (updates.isActive !== undefined) updateData.isActive = updates.isActive;

    if (updates.contactInfo) {
      updateData.contactInfo = {
        email: updates.contactInfo.email?.trim() ?? supplier.contactInfo.email,
        phone: updates.contactInfo.phone?.trim() ?? supplier.contactInfo.phone,
        address: updates.contactInfo.address?.trim() ?? supplier.contactInfo.address,
      };
    }

    await ctx.db.patch(id, updateData);

    // If supplier name changed, update denormalized data in products
    if (updates.name && updates.name.trim() !== supplier.name) {
      const products = await ctx.db
        .query("products")
        .withIndex("by_supplier", q => q.eq("supplierId", id))
        .collect();

      await Promise.all(
        products.map(product =>
          ctx.db.patch(product._id, {
            supplier: {
              id: supplier._id,
              name: updates.name!.trim(),
            },
          })
        )
      );
    }

    return { success: true };
  },
});

/**
 * Delete a supplier (soft delete by deactivating)
 * PERFORMANCE: Maintains referential integrity
 */
export const deleteSupplier = mutation({
  args: { id: v.id("suppliers") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_DELETE);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const supplier = await ctx.db.get(id);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Check if supplier has active products
    const activeProducts = await ctx.db
      .query("products")
      .withIndex("by_supplier_status", q =>
        q.eq("supplierId", id).eq("status", "active")
      )
      .collect();

    if (activeProducts.length > 0) {
      throw new Error("Cannot delete supplier with active products. Deactivate the products first.");
    }

    // Soft delete by deactivating
    await ctx.db.patch(id, {
      isActive: false,
    });

    return { success: true };
  },
});
