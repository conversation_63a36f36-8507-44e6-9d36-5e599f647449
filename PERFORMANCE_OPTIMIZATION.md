# Performance Optimization Guide

## Current Performance Issues

### 1. Query Performance Problems

**Identified Bottlenecks**:
- Complex nested object queries taking 2-5 seconds
- Vector search operations consuming excessive memory
- Aggregate queries causing timeout issues
- Inefficient pagination patterns

**Root Causes**:
- Over-use of `.collect()` instead of pagination
- Missing or inefficient database indexes
- Complex type inference slowing compilation
- Excessive aggregate instances

### 2. Memory Usage Issues

**Current Memory Consumption**:
- Development: 4GB+ RAM usage
- TypeScript compilation: 2GB+ memory
- Vector embeddings: ~2.4GB for 200K products
- Aggregate system: ~500MB overhead

## Optimization Strategies

### 1. Query Optimization

**Replace .collect() with Pagination**:
```typescript
// BEFORE (Problematic)
const allProducts = await ctx.db.query("products").collect();

// AFTER (Optimized)
const getProductsPaginated = async (ctx: QueryCtx, limit = 100) => {
  let cursor = null;
  const results = [];
  
  do {
    const page = await ctx.db.query("products")
      .paginate({ numItems: limit, cursor });
    
    results.push(...page.page);
    cursor = page.continueCursor;
  } while (cursor && results.length < 1000); // Safety limit
  
  return results;
};
```

**Optimize Index Usage**:
```typescript
// BEFORE (Table scan)
const products = await ctx.db.query("products")
  .filter(q => q.eq(q.field("status"), "active"))
  .collect();

// AFTER (Index scan)
const products = await ctx.db.query("products")
  .withIndex("by_status", q => q.eq("status", "active"))
  .take(100);
```

### 2. Aggregate System Optimization

**Current Issues**:
- 8 aggregate instances creating type complexity
- Redundant aggregates for simple counts
- Memory overhead for each aggregate

**Optimized Configuration**:
```typescript
// convex/convex.config.ts - Reduced aggregates
const app = defineApp();
app.use(aggregate, { name: "productStats" });    // Keep - essential
app.use(aggregate, { name: "orderStats" });      // Keep - essential  
app.use(aggregate, { name: "supplierStats" });   // Keep - essential
// Remove: productStock, orderCount, orderMonthlySales, groupBuyStats, userCount
export default app;
```

**Replace Simple Aggregates with Direct Queries**:
```typescript
// BEFORE (Using aggregate for simple count)
const totalUsers = await userCountAggregate.count(ctx);

// AFTER (Direct query with pagination)
const getUserCount = async (ctx: QueryCtx) => {
  let count = 0;
  let cursor = null;
  
  do {
    const page = await ctx.db.query("users")
      .paginate({ numItems: 1000, cursor });
    count += page.page.length;
    cursor = page.continueCursor;
  } while (cursor);
  
  return count;
};
```

### 3. Vector Search Optimization

**Current Issues**:
- 1536-dimension embeddings stored inline
- Memory exhaustion during vector operations
- Slow similarity searches

**Optimized Approach**:
```typescript
// Separate embeddings table
productEmbeddings: defineTable({
  productId: v.id("products"),
  embedding: v.array(v.float64()),
  model: v.string(),
  createdAt: v.number(),
})
.index("by_product", ["productId"])
.vectorIndex("by_embedding", {
  vectorField: "embedding",
  dimensions: 1536,
  filterFields: ["productId"],
});

// Lazy loading pattern
export const searchSimilarProducts = action({
  args: {
    productId: v.id("products"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { productId, limit = 10 }) => {
    // Get embedding for target product
    const embedding = await ctx.runQuery(internal.embeddings.getProductEmbedding, {
      productId,
    });
    
    if (!embedding) {
      return [];
    }
    
    // Perform vector search
    const similar = await ctx.vectorSearch("productEmbeddings", "by_embedding", {
      vector: embedding,
      limit: Math.min(limit, 50), // Reasonable limit
    });
    
    // Fetch product details in batch
    const productIds = similar.map(s => s.productId);
    const products = await ctx.runQuery(internal.products.fetchProductsByIds, {
      ids: productIds,
    });
    
    return products.map((product, index) => ({
      ...product,
      similarity: similar[index]._score,
    }));
  },
});
```

### 4. Batch Processing Optimization

**Efficient Bulk Operations**:
```typescript
export const processBulkUpdates = internalMutation({
  args: {
    updates: v.array(v.object({
      productId: v.id("products"),
      changes: v.any(),
    })),
    batchSize: v.optional(v.number()),
  },
  handler: async (ctx, { updates, batchSize = 50 }) => {
    const results = [];
    
    // Process in smaller batches to avoid limits
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      const batchResults = await Promise.all(
        batch.map(async ({ productId, changes }) => {
          try {
            await ctx.db.patch(productId, changes);
            return { productId, success: true };
          } catch (error) {
            return { 
              productId, 
              success: false, 
              error: error.message 
            };
          }
        })
      );
      
      results.push(...batchResults);
      
      // Add small delay between batches to prevent overwhelming
      if (i + batchSize < updates.length) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
    
    return {
      total: updates.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results,
    };
  },
});
```

## Performance Monitoring

### 1. Query Performance Tracking

```typescript
// Wrapper for performance monitoring
export const withPerformanceTracking = <T>(
  queryName: string,
  queryFn: () => Promise<T>
): Promise<T & { _performance?: { duration: number; timestamp: number } }> => {
  return async () => {
    const startTime = Date.now();
    const result = await queryFn();
    const duration = Date.now() - startTime;
    
    // Log slow queries
    if (duration > 1000) {
      console.warn(`Slow query: ${queryName} took ${duration}ms`);
    }
    
    return {
      ...result,
      _performance: {
        duration,
        timestamp: startTime,
      },
    };
  };
};

// Usage example
export const getProductsWithTracking = query({
  args: { limit: v.optional(v.number()) },
  handler: withPerformanceTracking(
    "getProducts",
    async (ctx, { limit = 20 }) => {
      return await ctx.db.query("products")
        .withIndex("by_status", q => q.eq("status", "active"))
        .take(limit);
    }
  ),
});
```

### 2. Memory Usage Monitoring

```typescript
export const getSystemMetrics = internalQuery({
  args: {},
  handler: async (ctx) => {
    const startTime = Date.now();
    
    // Sample queries to measure performance
    const [productCount, orderCount, supplierCount] = await Promise.all([
      ctx.db.query("products").paginate({ numItems: 1, cursor: null }),
      ctx.db.query("orders").paginate({ numItems: 1, cursor: null }),
      ctx.db.query("suppliers").paginate({ numItems: 1, cursor: null }),
    ]);
    
    const queryTime = Date.now() - startTime;
    
    return {
      metrics: {
        estimatedProductCount: productCount.page.length > 0 ? "1000+" : "0",
        estimatedOrderCount: orderCount.page.length > 0 ? "100+" : "0",
        estimatedSupplierCount: supplierCount.page.length > 0 ? "10+" : "0",
        queryResponseTime: queryTime,
        timestamp: Date.now(),
      },
      health: {
        status: queryTime < 500 ? "healthy" : "degraded",
        issues: queryTime > 1000 ? ["slow_queries"] : [],
      },
    };
  },
});
```

## Caching Strategies

### 1. Query Result Caching

```typescript
// Simple in-memory cache for frequently accessed data
const queryCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

export const getCachedSuppliers = query({
  args: {},
  handler: async (ctx) => {
    const cacheKey = "active_suppliers";
    const cached = queryCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.data;
    }
    
    const suppliers = await ctx.db.query("suppliers")
      .withIndex("by_active", q => q.eq("isActive", true))
      .collect();
    
    queryCache.set(cacheKey, {
      data: suppliers,
      timestamp: Date.now(),
    });
    
    return suppliers;
  },
});
```

### 2. Redis Integration (Production)

```typescript
// lib/cache.ts
import { Redis } from "@upstash/redis";

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

export const cacheGet = async <T>(key: string): Promise<T | null> => {
  try {
    const cached = await redis.get(key);
    return cached as T;
  } catch (error) {
    console.warn("Cache get failed:", error);
    return null;
  }
};

export const cacheSet = async (
  key: string,
  value: any,
  ttlSeconds = 300
): Promise<void> => {
  try {
    await redis.setex(key, ttlSeconds, JSON.stringify(value));
  } catch (error) {
    console.warn("Cache set failed:", error);
  }
};
```

## Expected Performance Improvements

### 1. Query Performance
- **Before**: 2-5 seconds for complex queries
- **After**: 100-500ms for most queries
- **Improvement**: 4-10x faster

### 2. Memory Usage
- **Before**: 4GB+ during development
- **After**: 1-2GB during development  
- **Improvement**: 50-75% reduction

### 3. Compilation Time
- **Before**: 2+ minutes
- **After**: 10-30 seconds
- **Improvement**: 4-12x faster

### 4. Scalability
- **Before**: Fails at ~50K products
- **After**: Supports 200K+ products
- **Improvement**: 4x+ capacity

---

*Implement these optimizations progressively, starting with the most critical issues*
*Monitor performance metrics after each change*
*Expected total implementation time: 2-3 weeks*
