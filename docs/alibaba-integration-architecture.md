# Alibaba Data Integration Architecture

## Overview

This document outlines the architectural decisions for integrating Alibaba scraped product data (200 000+) into our e-commerce platform. The integration supports complex pricing structures (offers, variants, custom services) while maintaining scalability for future providers.

## Core Principles

1. **Provider-Agnostic Design**: Schema extensions accommodate multiple providers
2. **Complete Pricing Preservation**: All pricing dimensions (base, variants, services, quantity tiers) are captured
3. **Accurate Order Fulfillment**: Order data enables precise provider purchasing
4. **Scalable Processing**: Handles large datasets (200k+ products) efficiently

## Schema Extensions

### Products Table

```typescript
products: defineTable({
  // Existing fields...
  title: v.string(),
  description: v.string(),
  curationNotes: v.string(),
  supplierId: v.id("suppliers"),
  priceInYuan: v.number(),
  serviceFee: v.number(),
  finalPrice: v.number(),
  tags: v.array(v.string()),
  images: v.array(v.union(v.string(), v.id("_storage"))),
  imageEmbedding: v.optional(v.array(v.number())),
  stockCount: v.number(),
  status: v.union(v.literal("active"), v.literal("inactive"), v.literal("archived")),
  createdBy: v.id("users"),
  updatedBy: v.id("users"),

  // New provider-agnostic fields
  providerData: v.object({
    source: v.string(), // 'alibaba', 'taobao', etc.
    productUrl: v.string(), // Direct link to provider product
    providerId: v.string(), // Provider's internal product ID
    lastScraped: v.number(), // Timestamp
    // Extensible field for provider-specific data
    providerSpecificData: v.optional(v.any()), // Store unique provider fields (e.g., Pinduoduo group buy metadata)
  }),

  // Complex pricing structure - base tiers
  pricingTiers: v.array(v.object({
    minQuantity: v.number(),
    maxQuantity: v.optional(v.number()),
    price: v.number(),
    currency: v.string(),
    discountPercentage: v.optional(v.number()),
    // Enhanced for multi-provider compatibility
    regionCode: v.optional(v.string()), // For regional pricing (e.g., mainland vs HK)
    participantRequirement: v.optional(v.number()), // For group buy requirements
    timeConstraint: v.optional(v.object({
      startTime: v.number(),
      endTime: v.number()
    })), // For time-limited promotions
  })),

  // Product variants with pricing
  variants: v.array(v.object({
    type: v.string(), // 'color', 'size', 'material'
    name: v.string(), // 'Red', 'Large', 'Cotton'
    value: v.string(), // Actual value for ordering
    priceType: v.union(v.literal('modifier'), v.literal('absolute')), // How price is calculated
    priceModifier: v.optional(v.number()), // Additional cost (for modifier type)
    absolutePrice: v.optional(v.number()), // Fixed price (for absolute type)
    currency: v.optional(v.string()),
    availableQuantity: v.optional(v.number()),
    images: v.optional(v.array(v.string())), // Variant-specific images
  })),

  // Custom services offered
  customServices: v.array(v.object({
    name: v.string(),
    description: v.optional(v.string()),
    minQuantity: v.optional(v.number()),
    price: v.number(),
    currency: v.string(),
    isRequired: v.boolean(),
  })),

  // Enhanced attributes for filtering/search
  attributes: v.object({
    category: v.string(),
    subcategory: v.optional(v.string()),
    material: v.optional(v.string()),
    weight: v.optional(v.number()),
    weightUnit: v.optional(v.string()),
    dimensions: v.optional(v.object({
      length: v.number(),
      width: v.number(),
      height: v.number(),
      unit: v.string(),
    })),
  }),
})
```

### Orders Table Extensions

```typescript
orders: defineTable({
  // Existing fields...
  userId: v.id("users"),
  items: v.array(v.object({
    productId: v.id("products"),
    quantity: v.number(),
    priceAtTime: v.number(),
    title: v.string(),
  })),
  status: v.union(
    v.literal("new"),
    v.literal("sourcing"),
    v.literal("action_required"),
    v.literal("shipped"),
    v.literal("delivered"),
    v.literal("cancelled")
  ),
  trackingNumber: v.optional(v.string()),
  shippingAddress: v.object({
    name: v.string(),
    address: v.string(),
    city: v.string(),
    country: v.string(),
    postalCode: v.string(),
  }),
  communicationHistory: v.array(v.object({
    message: v.string(),
    fromAdmin: v.boolean(),
    adminUserId: v.optional(v.id("users")),
    timestamp: v.number(),
  })),
  issueResolution: v.optional(v.object({
    issue: v.string(),
    suggestedAlternatives: v.array(v.id("products")),
    resolution: v.optional(v.string()),
  })),
  totalAmount: v.number(),
  assignedTo: v.optional(v.id("users")),

  // Provider-specific order details
  providerOrderData: v.object({
    selectedVariant: v.optional(v.object({
      type: v.string(),
      name: v.string(),
      value: v.string(),
      priceType: v.string(),
      finalPrice: v.number(), // Calculated final price for this variant
    })),
    selectedQuantityTier: v.object({
      minQuantity: v.number(),
      price: v.number(),
      currency: v.string(),
    }),
    selectedCustomServices: v.array(v.object({
      name: v.string(),
      price: v.number(),
    })),
    totalProviderCost: v.number(), // What we pay to provider
    pricingBreakdown: v.object({
      basePrice: v.number(),
      variantAdjustment: v.number(),
      quantityDiscount: v.number(),
      customServicesTotal: v.number(),
      ourCommission: v.number(),
    }),
  }),
})
```

### Group Buys Table Updates

```typescript
groupBuys: defineTable({
  // Existing fields...
  productId: v.id("products"),
  targetTiers: v.array(v.object({
    quantity: v.number(),
    price: v.number(),
  })),
  currentParticipants: v.number(),
  status: v.union(v.literal("active"), v.literal("completed"), v.literal("expired")),
  startTime: v.number(),
  endTime: v.number(),
  createdBy: v.id("users"),

  // Link to provider pricing tiers
  providerPricingTiers: v.array(v.object({
    minParticipants: v.number(),
    pricePerUnit: v.number(),
    ourCommission: v.number(), // Our markup
    finalPrice: v.number(), // Price user pays
  })),
})
```

## Data Mapping Strategy

### AlibabaProduct → App Product Transformation

#### Field Mapping

| AlibabaProduct Field | App Product Field | Notes |
|---------------------|-------------------|-------|
| `name` | `title` | Direct mapping |
| `productUrl` | `providerData.productUrl` | Provider-specific data |
| `productImagePreviews` | `images` | After upload to Convex storage |
| `offers`/`offerList` | `pricingTiers` | Handle both array and object shapes |
| `variants` | `variants` | Handle both array and object shapes, preserve pricing |
| `customServices` | `customServices` | Direct mapping |
| `attributes` | `attributes` | Structured object mapping |
| `weight` | `attributes.weight` | Nested in attributes |
| `processingStatus` | Internal use only | Not stored in final product |

#### Variant Processing Logic

```typescript
function processVariants(alibabaVariants: Variant[] | VariantTiers): AppVariant[] {
  if (Array.isArray(alibabaVariants)) {
    // Shape A: Simple array
    return alibabaVariants.map(variant => ({
      type: variant.variantType || 'option',
      name: variant.variantName || '',
      value: variant.variantName || '',
      priceType: variant.price ? 'absolute' : 'modifier',
      absolutePrice: typeof variant.price === 'number' ? variant.price : undefined,
      priceModifier: typeof variant.price === 'number' ? variant.price : 0,
      currency: variant.currency,
      availableQuantity: variant.availableQuantity,
    }));
  } else {
    // Shape B: Object with variants list
    return alibabaVariants.variants.map(variant => ({
      type: variant.variantType === '颜色' ? 'color' :
            variant.variantType === '尺码' ? 'size' : 'option',
      name: variant.variantName,
      value: variant.variantName,
      priceType: 'absolute',
      absolutePrice: parseFloat(variant.price),
      currency: variant.currency,
      availableQuantity: parseInt(variant.availableQuantity),
    }));
  }
}
```

#### Offer Processing Logic

```typescript
function processOffers(alibabaOffers: Offer[] | OfferTiers): PricingTier[] {
  if (Array.isArray(alibabaOffers)) {
    // Shape A: Simple array
    return alibabaOffers.map(offer => ({
      minQuantity: offer.minQuantity,
      maxQuantity: undefined, // Alibaba doesn't specify max
      price: typeof offer.price === 'number' ? offer.price : parseFloat(offer.price as string),
      currency: offer.currency || 'CNY',
      discountPercentage: undefined,
    }));
  } else {
    // Shape B: Object with pricing tiers
    return alibabaOffers.pricing_tiers.map(tier => ({
      minQuantity: parseInt(tier.minQuantity),
      maxQuantity: undefined,
      price: parseFloat(tier.price),
      currency: tier.currency,
      discountPercentage: undefined,
    }));
  }
}
```

## Pricing Calculation Logic

### Three Pricing Scenarios

1. **Base Price Only**: Use `pricingTiers` for quantity-based pricing
2. **Variant Modifier**: `basePrice + variant.priceModifier`
3. **Variant Absolute**: Use `variant.absolutePrice` (overrides base price)

### Complete Pricing Flow

```mermaid
graph TD
    A[User Selects Product] --> B[Choose Quantity]
    B --> C[Choose Variant]
    C --> D[Add Custom Services]

    B --> E[Get Base Price from pricingTiers]
    C --> F[Apply Variant Price]
    D --> G[Add Custom Services Cost]

    E --> H[Calculate Subtotal]
    F --> H
    G --> H

    H --> I[Apply Quantity Discount]
    I --> J[Add Our Commission]
    J --> K[Final User Price]
```

### Pricing Breakdown Storage

Every order stores a complete pricing breakdown:

```typescript
pricingBreakdown: {
  basePrice: 100,           // Provider's base price
  variantAdjustment: 20,    // Additional cost for selected variant
  quantityDiscount: -10,    // Discount for bulk quantity
  customServicesTotal: 15,  // Sum of selected custom services
  ourCommission: 25,        // Our markup
}
```

## Integration Approach

### Phase 1: Data Processing Pipeline

1. **Batch Processing**: Process `data.json` in chunks (1000 products/batch)
2. **Filtering**: Apply business rules (min price, has images, valid offers)
3. **Supplier Creation**: Auto-create suppliers from provider data
4. **Product Creation**: Transform and store products with all variants/options
5. **Image Pipeline**: Download Alibaba images → Upload to Convex → Generate embeddings

### Phase 2: Order Fulfillment Enhancement

1. **Order Translation**: Convert app orders to provider-specific format
2. **Variant Selection**: Store user's choices for accurate purchasing
3. **Pricing Calculation**: Use selected tier + custom services for total cost
4. **Provider Integration**: API/webhook system for order placement

### Phase 3: Group Buy Integration

1. **Tier Mapping**: Use provider's pricing tiers as group buy levels
2. **Commission Calculation**: Add markup based on volume tiers
3. **Dynamic Pricing**: Update group prices as participant count changes

## Multi-Provider Compatibility

### Supported Provider Patterns

| Provider | Pattern | Schema Support |
|----------|---------|----------------|
| **Alibaba/1688** | Quantity tiers, variants, custom services | ✅ Full support |
| **Taobao** | SKU variants, modifier pricing | ✅ Full support |
| **Pinduoduo** | Group buy tiers, participant requirements | ✅ Full support with `participantRequirement` |
| **Regional Pricing** | Mainland vs HK pricing | ✅ Full support with `regionCode` |
| **Time-Limited Promotions** | Flash sales, pre-sales | ✅ Full support with `timeConstraint` |

### Provider-Specific Data Handling

The `providerSpecificData` field allows storing unique provider information:

```typescript
// Example for Pinduoduo group buy metadata
providerSpecificData: {
  groupBuySettings: {
    maxGroupSize: 50,
    autoCloseTime: 3600, // seconds
    refundPolicy: "no_refund_after_join"
  }
}

// Example for Taobao SKU system
providerSpecificData: {
  skuProperties: {
    colorPropertyId: "1627207",
    sizePropertyId: "20509"
  }
}
```

### Regional Pricing Implementation

```typescript
// Example usage of regionCode
pricingTiers: [
  {
    minQuantity: 1,
    price: 100,
    currency: "CNY",
    regionCode: "mainland"
  },
  {
    minQuantity: 1,
    price: 120,
    currency: "HKD",
    regionCode: "hongkong"
  }
]
```

## Scalability Considerations

### Provider-Agnostic Design

- All provider-specific data in `providerData` object
- Standardized field names across providers
- Extensible `attributes` object for provider-specific metadata
- `providerSpecificData` field for unique provider requirements

### Performance Optimization

- Batch processing for large datasets
- Lazy loading of images/embeddings
- Indexed fields for efficient querying
- Caching layer for frequently accessed products

### Future Provider Integration

- Template-based mapping system
- Pluggable transformation modules
- Standardized provider interface
- Extensible pricing structures for new patterns

## Implementation Checklist

- [ ] Extend Convex schema with new fields
- [ ] Create data transformation utilities
- [ ] Implement batch import script
- [ ] Add image processing pipeline
- [ ] Update product creation workflow
- [ ] Modify order placement logic
- [ ] Enhance group buy calculations
- [ ] Update frontend components for variants/pricing display
- [ ] Add provider-specific order translation
- [ ] Test with sample Alibaba data
- [ ] Document provider integration patterns

## Key Decisions

1. **Schema Extension**: Extended existing `products` table rather than creating separate provider tables
2. **Variant Pricing**: Support both modifier and absolute pricing for variants
3. **Order Preservation**: Store complete pricing breakdown for transparency and accurate purchasing
4. **Batch Processing**: Handle large datasets through chunked processing
5. **Provider Agnostic**: Design schema to accommodate multiple providers from the start

This architecture enables accurate order fulfillment while maintaining the curated experience our users expect.

## Admin Workflows

### 1. Manual Product Creation & Editing Workflow

```mermaid
flowchart TD
    A[Admin Opens Product Creation Form] --> B[Enter Basic Product Info]
    B --> C[Select Supplier]
    C --> D[Upload Product Images]
    D --> E[Configure Provider Data]

    E --> F[Set Up Pricing Tiers]
    F --> G[Configure Variants]
    G --> H[Add Custom Services]
    H --> I[Set Product Attributes]

    I --> J[Review Pricing Preview]
    J --> K[Generate Image Embeddings]
    K --> L[Save Product]

    L --> M[Product Created Successfully]

    %% Editing workflow
    N[Admin Opens Existing Product] --> O[Modify Any Field]
    O --> P[Recalculate Pricing]
    P --> Q[Regenerate Embeddings if Images Changed]
    Q --> R[Update Product]

    %% Styling
    classDef process fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef data fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px

    class A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R process
    class M success
```

**Detailed Steps:**

1. **Basic Information**: Title, description, curation notes
2. **Supplier Selection**: Link to existing or create new supplier
3. **Image Management**: Upload primary images, variant-specific images
4. **Provider Data**: Enter source URL, provider ID, scraping metadata
5. **Pricing Configuration**:
   - Set base pricing tiers with quantity ranges
   - Configure regional pricing if applicable
   - Set time-limited promotions
6. **Variant Setup**: Add color/size/material options with pricing modifiers
7. **Custom Services**: Configure additional services (engraving, packaging)
8. **Attributes**: Set category, dimensions, weight, materials
9. **Pricing Preview**: Show calculated prices for different scenarios
10. **AI Processing**: Generate embeddings for image search
11. **Validation**: Ensure all required fields are complete

### 2. Client Order Handling Workflow

```mermaid
flowchart TD
    A[New Order Received] --> B[Order Details Review]
    B --> C[Extract Provider Requirements]

    C --> D{Order Type?}
    D -->|Standard Order| E[Calculate Provider Cost]
    D -->|Group Buy Order| F[Link to Group Buy Campaign]
    D -->|Custom Order| G[Process Custom Services]

    E --> H[Generate Provider Order Data]
    F --> H
    G --> H

    H --> I[Create Provider Purchase Order]
    I --> J[Send to Supplier]

    J --> K[Track Provider Order Status]
    K --> L{Status Update?}

    L -->|Processing| M[Update Client: Processing]
    L -->|Shipped| N[Update Tracking Info]
    L -->|Delivered| O[Mark Order Complete]
    L -->|Issue| P[Handle Issue Resolution]

    P --> Q[Communicate with Client]
    Q --> R[Apply Resolution]
    R --> S[Update Order Status]

    %% Issue resolution subprocess
    P --> T[Find Alternative Products]
    T --> U[Calculate Price Difference]
    U --> V[Send Resolution Options]
    V --> W[Apply Client Choice]

    %% Styling
    classDef process fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef communication fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef issue fill:#ffebee,stroke:#b71c1c,stroke-width:2px

    class A,B,C,E,F,G,H,I,J,K,M,N,O,Q,R,S,T,U,V,W process
    class D,L decision
    class P issue
    class Q,V communication
```

**Key Features of Order Handling:**

1. **Provider Data Extraction**: Automatically pull selected variant, quantity tier, custom services
2. **Cost Calculation**: Break down base price + variant adjustment + custom services + quantity discount
3. **Transparent Communication**: Show pricing breakdown to client
4. **Issue Resolution**: Use image search and alternative products for out-of-stock scenarios
5. **Status Synchronization**: Keep client and provider order statuses aligned

### 3. Group Buy Creation Workflow

```mermaid
flowchart TD
    A[Admin Initiates Group Buy] --> B[Select Base Product]
    B --> C[Configure Campaign Settings]

    C --> D[Set Participant Tiers]
    D --> E[Configure Pricing Structure]

    E --> F[Define Time Window]
    F --> G[Set Minimum Participants]

    G --> H[Configure Rewards]
    H --> I[Set Up Auto-Fulfillment]

    I --> J[Preview Campaign]
    J --> K[Launch Campaign]

    K --> L[Monitor Participation]
    L --> M{Target Reached?}

    M -->|No| N[Send Progress Updates]
    N --> O[Extend Time if Needed]
    O --> L

    M -->|Yes| P[Lock Final Price]
    P --> Q[Notify Participants]
    Q --> R[Process Orders]

    %% Pricing configuration subprocess
    E --> S[Link Provider Pricing Tiers]
    S --> T[Add Commission Markup]
    T --> U[Set Final User Price]
    U --> V[Configure Regional Pricing]

    %% Styling
    classDef process fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef config fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px

    class A,B,C,D,E,F,G,H,I,J,K,L,N,O,P,Q,R,S,T,U,V process
    class M decision
    class C,D,E,F,G,H config
    class R success
```

**Group Buy Configuration:**

1. **Product Selection**: Choose from existing products with provider pricing tiers
2. **Tier Setup**: Map provider quantity tiers to participant requirements
3. **Dynamic Pricing**: Final price decreases as more participants join
4. **Time Management**: Set campaign duration with possible extensions
5. **Auto-Fulfillment**: Automatically process orders when targets are met
6. **Commission Calculation**: Add markup based on final tier reached

### 4. Group Buy Processing & Delivery Workflow

```mermaid
flowchart TD
    A[Group Buy Target Reached] --> B[Auto-Lock Final Price]
    B --> C[Notify All Participants]

    C --> D[Collect Participant Orders]
    D --> E[Consolidate Order Quantities]

    E --> F[Calculate Bulk Pricing]
    F --> G[Apply Provider Discounts]

    G --> H[Create Master Provider Order]
    H --> I[Split by Shipping Regions]

    I --> J[Send Orders to Supplier]
    J --> K[Track Provider Order Status]

    K --> L{All Items Ready?}
    L -->|No| M[Update Participants]
    M --> N[Handle Individual Issues]
    N --> L

    L -->|Yes| O[Arrange Consolidated Shipping]
    O --> P[Generate Tracking Numbers]

    P --> Q[Update All Participant Orders]
    Q --> R[Send Delivery Notifications]

    R --> S[Monitor Delivery Status]
    S --> T{All Delivered?}

    T -->|No| U[Handle Delivery Issues]
    U --> V[Communicate with Participants]
    V --> W[Resolve Issues]
    W --> S

    T -->|Yes| X[Mark Campaign Complete]
    X --> Y[Process Final Payments]
    Y --> Z[Send Completion Summary]

    %% Issue handling subprocess
    N --> AA[Find Alternative Products]
    AA --> BB[Calculate Price Adjustments]
    BB --> CC[Offer Solutions]
    CC --> DD[Apply Participant Choices]

    %% Styling
    classDef process fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef communication fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef issue fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px

    class A,B,C,D,E,F,G,H,I,J,K,M,N,O,P,Q,R,S,U,V,W,Y,Z,AA,BB,CC,DD process
    class L,T decision
    class C,M,Q,R,V,CC communication
    class N,U issue
    class Z success
```

**Complete Group Buy Lifecycle:**

1. **Campaign Success**: Automatic price locking when targets reached
2. **Order Consolidation**: Combine all participant orders for bulk purchasing
3. **Provider Negotiation**: Leverage group size for best pricing
4. **Shipping Optimization**: Consolidate deliveries where possible
5. **Issue Resolution**: Handle individual participant problems
6. **Transparent Communication**: Keep all participants informed throughout
7. **Final Settlement**: Process payments and delivery confirmations

## Workflow Integration Points

### Data Flow Between Workflows

```mermaid
flowchart LR
    A[Product Creation] --> B[Product Catalog]
    B --> C[Order Processing]
    B --> D[Group Buy Creation]

    C --> E[Provider Orders]
    D --> E

    E --> F[Supplier Communication]
    F --> G[Status Updates]

    G --> H[Client Notifications]
    H --> I[Issue Resolution]

    I --> J[Alternative Products]
    J --> B

    %% Styling
    classDef workflow fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef data fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef process fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class A,C,D,F,I workflow
    class B,E,G,H,J data
    class E,G process
```

This integrated workflow system ensures:
- **Data Consistency**: All processes work with the same product data structure
- **Efficient Communication**: Seamless information flow between admin, suppliers, and clients
- **Scalable Operations**: Handles both individual orders and group buys
- **Quality Service**: Comprehensive issue resolution and customer support